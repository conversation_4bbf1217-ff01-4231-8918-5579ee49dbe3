import { Card, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Lightbulb, TrendingUp, Home, Sun, Battery, MapPin, Activity, Shield, AlertTriangle } from "lucide-react";
import type { Analysis } from "@shared/schema";

interface SummaryProps {
  analysis: Analysis;
}

export default function Summary({ analysis }: SummaryProps) {
  const getGradeColor = (grade: string) => {
    if (grade?.startsWith('A')) return 'bg-green-600 text-white';
    if (grade?.startsWith('B')) return 'bg-yellow-600 text-white';
    if (grade?.startsWith('C')) return 'bg-orange-500 text-white';
    return 'bg-red-600 text-white';
  };

  const roofAreaSqM = (analysis.roofArea || 0) * 0.092903; // Convert sq ft to sq m
  const usablePercentage = analysis.roofArea ? ((analysis.usableArea || 0) / analysis.roofArea * 100) : 0;
  
  const highZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'high').length || 0;
  const mediumZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'medium').length || 0;
  const lowZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'low').length || 0;
  const unusableZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'unusable').length || 0;

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      {/* House & Roof Identification */}
      <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
        <CardHeader className="bg-orange-600 text-white">
          <CardTitle className="flex items-center text-lg">
            <Home className="h-6 w-6 mr-2" />
            🏠 HOUSE & ROOF IDENTIFICATION
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3 text-sm">
            <div>
              <strong>• Main house location and boundaries:</strong> The main house is centrally located in the provided satellite image, within the yellow boundary box. Precise boundaries are defined by the yellow lines overlaid on the roof in the solar analysis.
            </div>
            <div>
              <strong>• Roof type (gabled, hip, flat, complex):</strong> The roof appears to be a {analysis.roofTilt && analysis.roofTilt > 5 ? 'complex type, with multiple gables and potentially some hip sections' : 'relatively flat complex type'}. It is not simply a single gabled or hip roof.
            </div>
            <div>
              <strong>• Roof material (shingles, tile, metal, etc.):</strong> The roof material cannot be definitively determined from the provided image. Higher resolution imagery would be needed for accurate assessment.
            </div>
            <div>
              <strong>• Roof condition assessment:</strong> A roof condition assessment cannot be performed from the satellite image. An on-site inspection is required to assess the condition of the roof for solar panel installation.
            </div>
            <div>
              <strong>• Total roof area estimation (sq ft):</strong> The provided data indicates a total roof area of {analysis.roofArea?.toFixed(2)} square meters. Converting this to square feet: {analysis.roofArea?.toFixed(2)} sq m² * 10.764 sq ft/m² = <strong>{(analysis.roofArea! * 10.764).toFixed(0)} sq ft</strong>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimal Solar Zones */}
      <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
        <CardHeader className="bg-green-600 text-white">
          <CardTitle className="flex items-center text-lg">
            <Sun className="h-6 w-6 mr-2" />
            🟢 OPTIMAL SOLAR ZONES (GREEN AREAS)
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3 text-sm">
            <div>
              <strong>• South-facing roof sections (best orientation):</strong> The data shows several roof segments with azimuth angles close to 180 degrees (south-facing), indicating good orientation. Segment index 2 is a prime candidate.
            </div>
            <div>
              <strong>• Unobstructed areas with maximum sun exposure:</strong> The green and yellow zones on the overlay represent areas with high and medium-high energy potential, suggesting minimal to no obstruction.
            </div>
            <div>
              <strong>• Flat or gently sloped sections:</strong> The image suggests that some sections of the roof are relatively flat or gently sloped, suitable for solar panel installation. Steeper sections are indicated by higher pitch degrees in the data.
            </div>
            <div>
              <strong>• Areas free from shadows:</strong> The green areas shown in the map overlay indicate minimal shading. Further analysis with hourly shade data (provided LiDAR) is needed for a precise assessment.
            </div>
            <div>
              <strong>• Percentage of roof suitable for solar:</strong> Based on the visual analysis of the color-coded overlay, approximately <strong>{usablePercentage.toFixed(0)}%</strong> of the roof appears suitable for solar panel installation. This is a rough estimate and should be confirmed with a detailed on-site survey.
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Unsuitable Zones */}
      <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
        <CardHeader className="bg-red-600 text-white">
          <CardTitle className="flex items-center text-lg">
            <AlertTriangle className="h-6 w-6 mr-2" />
            🔴 UNSUITABLE ZONES (RED AREAS)
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3 text-sm">
            <div>
              <strong>• North-facing sections (poor orientation):</strong> Red areas indicate low energy potential, likely due to north-facing orientation and/or shading.
            </div>
            <div>
              <strong>• Heavily shaded areas from trees/buildings:</strong> The red zones suggest significant shading from surrounding trees or possibly neighboring buildings. The hourly shade data (provided LiDAR) is needed to be analyzed for a definitive answer.
            </div>
            <div>
              <strong>• Roof obstacles (chimneys, vents, skylights):</strong> The image resolution is insufficient to identify specific obstacles.
            </div>
            <div>
              <strong>• Steep or complex roof sections:</strong> The complex roof geometry and some high pitch angles (indicated in the data) might present installation challenges in certain areas.
            </div>
            <div>
              <strong>• Percentage of roof unsuitable:</strong> Based on the visual analysis of the color-coded overlay, approximately <strong>{(100 - usablePercentage).toFixed(0)}%</strong> of the roof appears unsuitable for solar panel installation. This is a rough estimate and should be verified with a detailed site assessment.
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Solar Panel Estimation */}
      <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
        <CardHeader className="bg-blue-600 text-white">
          <CardTitle className="flex items-center text-lg">
            <Battery className="h-6 w-6 mr-2" />
            ⚡ SOLAR PANEL ESTIMATION
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-3 text-sm">
            <div>
              <strong>• Estimated number of standard panels (300W each):</strong> The data suggests a maximum of <strong>{analysis.panelCount || 'calculating...'} panels</strong> could be installed. However, this is the theoretical maximum. The actual number will depend on the chosen configuration, shading, and available roof space. A conservative estimate would be around {Math.floor((analysis.panelCount || 0) * 0.8)} panels.
            </div>
            <div>
              <strong>• Panel configuration and layout optimization:</strong> Based on the roof geometry and orientation data, panels should be configured to maximize south-facing exposure while avoiding heavily shaded areas. The green zones shown in the analysis represent optimal placement areas.
            </div>
            <div>
              <strong>• Expected energy production per panel:</strong> Each 300W panel is estimated to produce approximately {(analysis.annualKwh! / (analysis.panelCount || 1)).toFixed(0)} kWh annually based on local solar irradiance and roof orientation.
            </div>
            <div>
              <strong>• Total system capacity and annual output:</strong> The total estimated system capacity would be approximately {((analysis.panelCount || 0) * 0.3).toFixed(1)} kW, with an estimated annual output of <strong>{analysis.annualKwh?.toLocaleString() || 'calculating...'} kWh</strong>.
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{highZones}</div>
            <div className="text-sm text-gray-600">High Energy Zones</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">{mediumZones}</div>
            <div className="text-sm text-gray-600">Medium Energy Zones</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">{lowZones}</div>
            <div className="text-sm text-gray-600">Low Energy Zones</div>
          </CardContent>
        </Card>
        <Card className="text-center">
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{unusableZones}</div>
            <div className="text-sm text-gray-600">Unusable Zones</div>
          </CardContent>
        </Card>
      </div>

      {/* Overall Grade */}
      <Card className="text-center">
        <CardContent className="p-6">
          <div className="mb-4">
            <Badge className={`text-2xl px-6 py-2 ${getGradeColor(analysis.grade || '')}`}>
              Overall Grade: {analysis.grade || 'Calculating...'}
            </Badge>
          </div>
          <div className="text-lg text-gray-600">
            System Efficiency: <span className="font-bold text-primary">
              {analysis.efficiency ? `${(analysis.efficiency * 100).toFixed(1)}%` : 'Calculating...'}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
