import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertAnalysisSchema, updateAnalysisSchema } from "@shared/schema";
import { z } from "zod";

// Import services
import { GeocodingService } from "./services/geocoding";
import { ImageryService } from "./services/imagery";
import { AIAnalysisService } from "./services/ai-analysis";
import { SolarCalculationsService } from "./services/solar-calculations";
import { GeminiService } from "./services/gemini";
import { ReportGeneratorService } from "./services/report-generator";
import { DataStorageMiddleware } from "./middleware/data-storage";

export async function registerRoutes(app: Express): Promise<Server> {
  // Initialize services
  const geocodingService = new GeocodingService();
  const imageryService = new ImageryService();
  const aiAnalysisService = new AIAnalysisService();
  const solarCalculationsService = new SolarCalculationsService();
  const geminiService = new GeminiService();
  const reportGeneratorService = new ReportGeneratorService();
  const dataStorage = DataStorageMiddleware.getInstance();

  // Validation schemas
  const geocodeSchema = z.object({
    address: z.string().min(1, "Address is required"),
  });

  const fetchImagesSchema = z.object({
    latitude: z.number(),
    longitude: z.number(),
  });

  const analyzeSchema = z.object({
    address: z.string().min(1, "Address is required"),
  });

  // GET /api/analyses - Get recent analyses
  app.get("/api/analyses", async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const analyses = await storage.getRecentAnalyses(limit);
      res.json(analyses);
    } catch (error) {
      console.error("Error fetching analyses:", error);
      res.status(500).json({ error: "Failed to fetch analyses" });
    }
  });

  // GET /api/analyses/:id - Get specific analysis
  app.get("/api/analyses/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const analysis = await storage.getAnalysis(id);
      
      if (!analysis) {
        return res.status(404).json({ error: "Analysis not found" });
      }
      
      res.json(analysis);
    } catch (error) {
      console.error("Error fetching analysis:", error);
      res.status(500).json({ error: "Failed to fetch analysis" });
    }
  });

  // POST /api/geocode - Geocode address
  app.post("/api/geocode", async (req, res) => {
    try {
      const { address } = geocodeSchema.parse(req.body);
      const result = await geocodingService.geocodeAddress(address);
      res.json(result);
    } catch (error) {
      console.error("Geocoding error:", error);
      res.status(400).json({ 
        error: error instanceof Error ? error.message : "Geocoding failed" 
      });
    }
  });

  // POST /api/fetch-images - Fetch satellite and street view images
  app.post("/api/fetch-images", async (req, res) => {
    try {
      const { latitude, longitude } = fetchImagesSchema.parse(req.body);
      const result = await imageryService.fetchAllImages(latitude, longitude);
      res.json(result);
    } catch (error) {
      console.error("Image fetching error:", error);
      res.status(400).json({ 
        error: error instanceof Error ? error.message : "Image fetching failed" 
      });
    }
  });

  // POST /api/analyze - Run complete analysis
  app.post("/api/analyze", async (req, res) => {
    try {
      const { address } = analyzeSchema.parse(req.body);
      
      // Create initial analysis record
      const analysis = await storage.createAnalysis({
        address,
        latitude: 0,
        longitude: 0,
        status: "processing",
      });

      // Run analysis in background
      (async () => {
        try {
          // Step 1: Geocode address
          const geocodeResult = await geocodingService.geocodeAddress(address);
          
          await storage.updateAnalysis(analysis.id, {
            latitude: geocodeResult.latitude,
            longitude: geocodeResult.longitude,
            address: geocodeResult.formattedAddress,
          });

          // Step 2: Fetch images
          const imageResult = await imageryService.fetchAllImages(
            geocodeResult.latitude, 
            geocodeResult.longitude
          );

          await storage.updateAnalysis(analysis.id, {
            satelliteImageUrl: imageResult.satelliteImageUrl,
            streetViewUrls: imageResult.streetViewUrls,
          });

          // Step 3: AI analysis
          const aiResult = await aiAnalysisService.performCompleteAnalysis(
            imageResult.localPaths.satellite,
            geocodeResult.latitude,
            geocodeResult.longitude
          );

          // Step 4: Solar calculations
          const solarResult = solarCalculationsService.calculateSolarPotential(
            geocodeResult.latitude,
            geocodeResult.longitude,
            aiResult.roofResult.roofArea,
            aiResult.roofResult.usableArea,
            aiResult.solarResult.zones
          );

          // Step 5: Generate AI summary
          const summaryResult = await geminiService.generateSummary({
            address: geocodeResult.formattedAddress,
            roofArea: aiResult.roofResult.roofArea,
            usableArea: aiResult.roofResult.usableArea,
            panelCount: solarResult.panelCount,
            annualKwh: solarResult.annualKwh,
            efficiency: solarResult.efficiency,
            grade: solarResult.grade,
            roofTilt: solarResult.roofTilt,
            orientation: solarResult.orientation,
            shadingLevel: solarResult.shadingLevel,
          });

          // Step 6: Generate reports
          const reportData = {
            analysis: {
              address: geocodeResult.formattedAddress,
              roofArea: aiResult.roofResult.roofArea,
              usableArea: aiResult.roofResult.usableArea,
              panelCount: solarResult.panelCount,
              annualKwh: solarResult.annualKwh,
              efficiency: solarResult.efficiency,
              grade: solarResult.grade,
              roofTilt: solarResult.roofTilt,
              orientation: solarResult.orientation,
              shadingLevel: solarResult.shadingLevel,
            },
            solarGrid: aiResult.solarResult,
            summary: summaryResult.summary,
            recommendations: summaryResult.recommendations,
          };

          const pdfPath = await reportGeneratorService.generatePDFReport(reportData);
          const csvPath = await reportGeneratorService.generateCSVReport(reportData);

          // Final update
          await storage.updateAnalysis(analysis.id, {
            status: "completed",
            roofArea: aiResult.roofResult.roofArea,
            usableArea: aiResult.roofResult.usableArea,
            panelCount: solarResult.panelCount,
            annualKwh: solarResult.annualKwh,
            efficiency: solarResult.efficiency,
            grade: solarResult.grade,
            roofTilt: solarResult.roofTilt,
            orientation: solarResult.orientation,
            shadingLevel: solarResult.shadingLevel,
            solarGrid: aiResult.solarResult,
            aiSummary: summaryResult.summary,
            recommendations: summaryResult.recommendations,
            pdfReportPath: pdfPath,
            csvReportPath: csvPath,
          });

        } catch (error) {
          console.error("Analysis failed:", error);
          await storage.updateAnalysis(analysis.id, {
            status: "failed",
          });
        }
      })();

      res.json({ analysisId: analysis.id, status: "processing" });
    } catch (error) {
      console.error("Analysis start error:", error);
      res.status(400).json({ 
        error: error instanceof Error ? error.message : "Analysis failed to start" 
      });
    }
  });

  // GET /api/analyses/:id/report/pdf - Download PDF report
  app.get("/api/analyses/:id/report/pdf", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const analysis = await storage.getAnalysis(id);
      
      if (!analysis || !analysis.pdfReportPath) {
        return res.status(404).json({ error: "Report not found" });
      }
      
      const fileData = dataStorage.getFile(analysis.pdfReportPath);
      res.setHeader('Content-Type', 'text/html');
      res.setHeader('Content-Disposition', `attachment; filename="solar_report_${id}.html"`);
      res.send(fileData);
    } catch (error) {
      console.error("PDF download error:", error);
      res.status(500).json({ error: "Failed to download PDF report" });
    }
  });

  // GET /api/analyses/:id/report/csv - Download CSV report
  app.get("/api/analyses/:id/report/csv", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const analysis = await storage.getAnalysis(id);
      
      if (!analysis || !analysis.csvReportPath) {
        return res.status(404).json({ error: "Report not found" });
      }
      
      const fileData = dataStorage.getFile(analysis.csvReportPath);
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="solar_data_${id}.csv"`);
      res.send(fileData);
    } catch (error) {
      console.error("CSV download error:", error);
      res.status(500).json({ error: "Failed to download CSV report" });
    }
  });

  // Cleanup old files periodically
  setInterval(() => {
    dataStorage.cleanupOldFiles(24); // Remove files older than 24 hours
  }, 60 * 60 * 1000); // Run every hour

  const httpServer = createServer(app);
  return httpServer;
}
