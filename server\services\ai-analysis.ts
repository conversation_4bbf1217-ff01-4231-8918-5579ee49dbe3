import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';

export interface RoofDetectionResult {
  roofArea: number; // sq ft
  usableArea: number; // sq ft
  roofPolygon: Array<{x: number, y: number}>;
  confidence: number;
}

export interface SolarGridZone {
  x: number;
  y: number;
  width: number;
  height: number;
  potential: 'high' | 'medium' | 'low' | 'unusable';
  efficiency: number;
  estimatedOutput: number;
}

export interface SolarGridResult {
  zones: SolarGridZone[];
  totalPanels: number;
  totalOutput: number;
}

export class AIAnalysisService {
  private dataDir: string;
  private cvBackendUrl: string;

  constructor() {
    this.dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
    
    // Python CV backend URL
    this.cvBackendUrl = process.env.CV_BACKEND_URL || 'http://localhost:8000';
  }

  async detectRoof(imagePath: string): Promise<RoofDetectionResult> {
    // In a real implementation, this would use YOLOv8 for roof detection
    // For now, we'll simulate the analysis based on typical roof characteristics
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock roof detection results - in production this would use actual ML models
      const imageBuffer = fs.readFileSync(imagePath);
      const imageSize = imageBuffer.length;
      
      // Simulate roof detection based on image characteristics
      const roofArea = Math.floor(Math.random() * 1000 + 1500); // 1500-2500 sq ft
      const usableArea = roofArea * (0.7 + Math.random() * 0.2); // 70-90% usable
      
      const roofPolygon = [
        { x: 0.2, y: 0.3 },
        { x: 0.8, y: 0.3 },
        { x: 0.8, y: 0.7 },
        { x: 0.2, y: 0.7 }
      ];

      return {
        roofArea,
        usableArea,
        roofPolygon,
        confidence: 0.85 + Math.random() * 0.1
      };
    } catch (error) {
      throw new Error(`Roof detection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateSolarGrid(roofResult: RoofDetectionResult, latitude: number, longitude: number): Promise<SolarGridResult> {
    // Generate realistic solar panel grid zones that properly align with roof areas
    
    const zones: SolarGridZone[] = [];
    
    // Create a more realistic grid pattern based on the roof polygon
    const roofBounds = {
      minX: Math.min(...roofResult.roofPolygon.map(p => p.x)),
      maxX: Math.max(...roofResult.roofPolygon.map(p => p.x)),
      minY: Math.min(...roofResult.roofPolygon.map(p => p.y)),
      maxY: Math.max(...roofResult.roofPolygon.map(p => p.y))
    };
    
    const roofWidth = roofBounds.maxX - roofBounds.minX;
    const roofHeight = roofBounds.maxY - roofBounds.minY;
    
    // Generate a grid of small zones (like panels) across the roof area
    const zoneSize = 0.015; // Small zone size for detailed grid
    const cols = Math.floor(roofWidth / zoneSize);
    const rows = Math.floor(roofHeight / zoneSize);
    
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const x = roofBounds.minX + (col * zoneSize);
        const y = roofBounds.minY + (row * zoneSize);
        
        // Check if this zone is within the roof polygon
        if (this.isPointInRoof(x + zoneSize/2, y + zoneSize/2, roofResult.roofPolygon)) {
          // Calculate solar potential based on various factors
          const distanceFromSouth = Math.abs(y - 0.3); // South-facing is optimal (lower y values)
          const edgeDistance = Math.min(
            x - roofBounds.minX,
            roofBounds.maxX - x,
            y - roofBounds.minY,
            roofBounds.maxY - y
          ) / Math.min(roofWidth, roofHeight);
          
          // Add some randomness for realistic shading patterns
          const randomFactor = 0.8 + Math.random() * 0.4;
          const latitudeFactor = Math.max(0.6, 1 - Math.abs(latitude - 35) / 50);
          
          // South-facing areas are better, edge areas might have more shading
          const orientationFactor = 1 - (distanceFromSouth * 2);
          const shadingFactor = Math.min(1, edgeDistance * 3 + 0.4);
          
          const efficiency = Math.min(0.95, 
            orientationFactor * shadingFactor * latitudeFactor * randomFactor * 0.9
          );
          
          let potential: 'high' | 'medium' | 'low' | 'unusable';
          if (efficiency > 0.75) potential = 'high';
          else if (efficiency > 0.55) potential = 'medium';
          else if (efficiency > 0.35) potential = 'low';
          else potential = 'unusable';
          
          // Add some strategic unusable zones for chimneys, vents, etc.
          let finalEfficiency = efficiency;
          if (Math.random() < 0.08 && potential !== 'unusable') {
            potential = 'unusable';
            finalEfficiency = 0.1;
          }
          
          const estimatedOutput = finalEfficiency * 400; // 400W per panel at 100% efficiency
          
          zones.push({
            x,
            y,
            width: zoneSize,
            height: zoneSize,
            potential,
            efficiency: finalEfficiency,
            estimatedOutput
          });
        }
      }
    }
    
    const totalOutput = zones.reduce((sum, zone) => sum + zone.estimatedOutput, 0);
    const totalPanels = zones.filter(z => z.potential !== 'unusable').length;
    
    return {
      zones,
      totalPanels,
      totalOutput
    };
  }
  
  private isPointInRoof(x: number, y: number, polygon: Array<{x: number, y: number}>): boolean {
    // Simple point-in-polygon test using ray casting
    let inside = false;
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      if (((polygon[i].y > y) !== (polygon[j].y > y)) &&
          (x < (polygon[j].x - polygon[i].x) * (y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
        inside = !inside;
      }
    }
    return inside;
  }

  async performCompleteAnalysis(imagePath: string, latitude: number, longitude: number): Promise<{
    roofResult: RoofDetectionResult;
    solarResult: SolarGridResult;
  }> {
    try {
      // Try to use the Python CV backend first
      const cvResult = await this.callPythonCVBackend(latitude, longitude, "Address from coordinates");
      
      if (cvResult) {
        return {
          roofResult: this.convertCVRoofResult(cvResult.roof_detection, cvResult.solar_analysis),
          solarResult: this.convertCVSolarResult(cvResult.solar_grid)
        };
      }
    } catch (error) {
      console.log('Python CV backend unavailable, falling back to mock analysis:', error);
    }
    
    // Fallback to mock analysis if CV backend is not available
    const roofResult = await this.detectRoof(imagePath);
    const solarResult = await this.generateSolarGrid(roofResult, latitude, longitude);
    
    return {
      roofResult,
      solarResult
    };
  }

  private async callPythonCVBackend(latitude: number, longitude: number, address: string): Promise<any> {
    try {
      const response = await axios.post(`${this.cvBackendUrl}/analyze-roof`, {
        latitude,
        longitude,
        address,
        zoom: 19
      }, {
        timeout: 30000 // 30 second timeout
      });
      
      return response.data;
    } catch (error) {
      console.error('CV Backend call failed:', error);
      throw error;
    }
  }

  private convertCVRoofResult(roofDetection: any, solarAnalysis: any): RoofDetectionResult {
    // Convert Python CV result to our interface
    const roofPolygon = roofDetection.roof_polygon.map((point: number[]) => ({
      x: point[0],
      y: point[1]
    }));
    
    // Convert square meters to square feet
    const roofAreaSqFt = (roofDetection.area_pixels || 2000) * 10.764;
    const usableAreaSqFt = roofAreaSqFt * 0.8; // 80% usable area
    
    return {
      roofArea: Math.round(roofAreaSqFt),
      usableArea: Math.round(usableAreaSqFt),
      roofPolygon,
      confidence: roofDetection.confidence
    };
  }

  private convertCVSolarResult(solarGrid: any): SolarGridResult {
    // Convert Python CV solar grid to our interface
    const zones = solarGrid.panels.map((panel: any) => ({
      x: panel.x,
      y: panel.y,
      width: panel.width,
      height: panel.height,
      potential: panel.potential as 'high' | 'medium' | 'low' | 'unusable',
      efficiency: panel.efficiency,
      estimatedOutput: panel.estimated_output
    }));
    
    return {
      zones,
      totalPanels: solarGrid.total_panels,
      totalOutput: solarGrid.total_output
    };
  }
}
