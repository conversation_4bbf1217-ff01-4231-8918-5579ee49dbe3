import { pgTable, text, serial, integer, real, timestamp, json, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const analyses = pgTable("analyses", {
  id: serial("id").primaryKey(),
  address: text("address").notNull(),
  latitude: real("latitude").notNull(),
  longitude: real("longitude").notNull(),
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  
  // Image URLs
  satelliteImageUrl: text("satellite_image_url"),
  streetViewUrls: json("street_view_urls").$type<string[]>(),
  
  // AI Analysis Results
  roofArea: real("roof_area"), // in sq ft
  usableArea: real("usable_area"), // in sq ft
  panelCount: integer("panel_count"),
  annualKwh: real("annual_kwh"),
  efficiency: real("efficiency"), // 0-1
  grade: text("grade"), // A+, A, A-, B+, etc.
  
  // Property Details
  roofTilt: real("roof_tilt"), // degrees
  orientation: text("orientation"), // South-facing, etc.
  shadingLevel: text("shading_level"), // Minimal, Moderate, High
  
  // Solar Grid Data
  solarGrid: json("solar_grid").$type<{
    zones: Array<{
      x: number;
      y: number;
      width: number;
      height: number;
      potential: 'high' | 'medium' | 'low' | 'unusable';
      efficiency: number;
      estimatedOutput: number;
    }>;
  }>(),
  
  // Generated Reports
  pdfReportPath: text("pdf_report_path"),
  csvReportPath: text("csv_report_path"),
  
  // NLP Summary
  aiSummary: text("ai_summary"),
  recommendations: json("recommendations").$type<string[]>(),
  
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const insertAnalysisSchema = createInsertSchema(analyses).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const updateAnalysisSchema = createInsertSchema(analyses).omit({
  id: true,
  createdAt: true,
}).partial();

export type Analysis = typeof analyses.$inferSelect;
export type InsertAnalysis = z.infer<typeof insertAnalysisSchema>;
export type UpdateAnalysis = z.infer<typeof updateAnalysisSchema>;

// Keep original user schema for compatibility
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
