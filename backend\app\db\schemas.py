"""
Pydantic schemas for request/response validation
"""
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
from datetime import datetime


class AnalysisBase(BaseModel):
    """Base analysis schema"""
    address: str
    latitude: float
    longitude: float


class AnalysisCreate(AnalysisBase):
    """Schema for creating analysis"""
    pass


class AnalysisUpdate(BaseModel):
    """Schema for updating analysis"""
    status: Optional[str] = None
    satellite_image_url: Optional[str] = None
    street_view_urls: Optional[List[str]] = None
    roof_area: Optional[float] = None
    usable_area: Optional[float] = None
    panel_count: Optional[int] = None
    annual_kwh: Optional[float] = None
    efficiency: Optional[float] = None
    grade: Optional[str] = None
    roof_tilt: Optional[float] = None
    orientation: Optional[str] = None
    shading_level: Optional[str] = None
    solar_grid: Optional[Dict[str, Any]] = None
    pdf_report_path: Optional[str] = None
    csv_report_path: Optional[str] = None
    ai_summary: Optional[str] = None
    recommendations: Optional[List[str]] = None


class AnalysisResponse(BaseModel):
    """Schema for analysis response"""
    id: int
    address: str
    latitude: float
    longitude: float
    status: str
    satelliteImageUrl: Optional[str] = None
    streetViewUrls: Optional[List[str]] = None
    roofArea: Optional[float] = None
    usableArea: Optional[float] = None
    panelCount: Optional[int] = None
    annualKwh: Optional[float] = None
    efficiency: Optional[float] = None
    grade: Optional[str] = None
    roofTilt: Optional[float] = None
    orientation: Optional[str] = None
    shadingLevel: Optional[str] = None
    solarGrid: Optional[Dict[str, Any]] = None
    pdfReportPath: Optional[str] = None
    csvReportPath: Optional[str] = None
    aiSummary: Optional[str] = None
    recommendations: Optional[List[str]] = None
    createdAt: Optional[str] = None
    updatedAt: Optional[str] = None

    class Config:
        from_attributes = True


class GeocodeRequest(BaseModel):
    """Schema for geocoding request"""
    address: str = Field(..., min_length=1, description="Address to geocode")


class GeocodeResponse(BaseModel):
    """Schema for geocoding response"""
    latitude: float
    longitude: float
    formattedAddress: str


class AnalyzeRequest(BaseModel):
    """Schema for analysis request"""
    address: str = Field(..., min_length=1, description="Address to analyze")


class AnalyzeResponse(BaseModel):
    """Schema for analysis response"""
    analysisId: int
    message: str


class SolarZone(BaseModel):
    """Schema for solar zone"""
    x: float
    y: float
    width: float
    height: float
    potential: str  # 'high', 'medium', 'low', 'unusable'
    efficiency: float
    estimatedOutput: float


class SolarGrid(BaseModel):
    """Schema for solar grid"""
    zones: List[SolarZone]


class ErrorResponse(BaseModel):
    """Schema for error responses"""
    detail: str
