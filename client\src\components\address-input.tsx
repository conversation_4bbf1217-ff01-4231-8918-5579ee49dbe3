import { useState } from "react";
import { MapPin, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, CardContent, <PERSON>H<PERSON>er, CardTitle } from "@/components/ui/card";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface AddressInputProps {
  onAnalysisStart: (analysisId: number) => void;
}

export default function AddressInput({ onAnalysisStart }: AddressInputProps) {
  const [address, setAddress] = useState("");
  const { toast } = useToast();

  const analyzeMutation = useMutation({
    mutationFn: async (address: string) => {
      const res = await apiRequest("POST", "/api/analyze", { address });
      return res.json();
    },
    onSuccess: (data) => {
      onAnalysisStart(data.analysisId);
      toast({
        title: "Analysis Started",
        description: "Your roof analysis is now in progress.",
      });
    },
    onError: (error) => {
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to start analysis",
        variant: "destructive",
      });
    },
  });

  const handleAnalyze = () => {
    if (!address.trim()) {
      toast({
        title: "Address Required",
        description: "Please enter a valid address to analyze.",
        variant: "destructive",
      });
      return;
    }
    analyzeMutation.mutate(address);
  };

  const handleSampleAddress = (sampleAddress: string) => {
    setAddress(sampleAddress);
  };

  const sampleAddresses = [
    "1600 Amphitheatre Parkway, Mountain View, CA",
    "1 Tesla Road, Austin, TX",
    "Apple Park, Cupertino, CA"
  ];

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-lg">Property Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <Label htmlFor="address" className="text-sm font-medium text-gray-700 mb-2">
              Property Address
            </Label>
            <div className="relative">
              <Input
                id="address"
                type="text"
                placeholder="Enter street address, city, state"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    handleAnalyze();
                  }
                }}
              />
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            </div>
          </div>
          <div className="flex items-end">
            <Button 
              onClick={handleAnalyze}
              disabled={analyzeMutation.isPending}
              className="px-8 py-3"
            >
              {analyzeMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Analyze Roof
                </>
              )}
            </Button>
          </div>
        </div>
        
        <div className="mt-4 pt-4 border-t">
          <p className="text-sm text-gray-600 mb-3">Try these sample addresses:</p>
          <div className="flex flex-wrap gap-2">
            {sampleAddresses.map((sampleAddress, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => handleSampleAddress(sampleAddress)}
                className="text-xs"
              >
                {sampleAddress}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
