import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export interface ImageryResult {
  satelliteImageUrl: string;
  streetViewUrls: string[];
  localPaths: {
    satellite: string;
    streetViews: string[];
  };
}

export class ImageryService {
  private apiKey: string;
  private dataDir: string;

  constructor() {
    this.apiKey = process.env.GOOGLE_API_KEY || process.env.GOOGLE_MAPS_API_KEY || "";
    if (!this.apiKey) {
      throw new Error("Google API key is required for imagery");
    }

    this.dataDir = path.join(__dirname, '../../data');
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  async fetchSatelliteImage(latitude: number, longitude: number, zoom: number = 19): Promise<string> {
    const size = '800x600';
    const maptype = 'satellite';
    
    const url = `https://maps.googleapis.com/maps/api/staticmap?center=${latitude},${longitude}&zoom=${zoom}&size=${size}&maptype=${maptype}&key=${this.apiKey}`;
    
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch satellite image: ${response.statusText}`);
    }

    const buffer = await response.arrayBuffer();
    const filename = `satellite_${latitude}_${longitude}_${Date.now()}.png`;
    const filepath = path.join(this.dataDir, filename);
    
    fs.writeFileSync(filepath, Buffer.from(buffer));
    
    return filepath;
  }

  async fetchStreetViewImages(latitude: number, longitude: number): Promise<string[]> {
    const size = '600x400';
    const headings = [0, 90, 180, 270]; // North, East, South, West
    const filepaths: string[] = [];

    for (const heading of headings) {
      const url = `https://maps.googleapis.com/maps/api/streetview?size=${size}&location=${latitude},${longitude}&heading=${heading}&pitch=10&key=${this.apiKey}`;
      
      const response = await fetch(url);
      if (!response.ok) {
        console.warn(`Failed to fetch street view image for heading ${heading}: ${response.statusText}`);
        continue;
      }

      const buffer = await response.arrayBuffer();
      const filename = `streetview_${latitude}_${longitude}_${heading}_${Date.now()}.png`;
      const filepath = path.join(this.dataDir, filename);
      
      fs.writeFileSync(filepath, Buffer.from(buffer));
      filepaths.push(filepath);
    }

    return filepaths;
  }

  async fetchAllImages(latitude: number, longitude: number): Promise<ImageryResult> {
    const [satelliteImagePath, streetViewPaths] = await Promise.all([
      this.fetchSatelliteImage(latitude, longitude),
      this.fetchStreetViewImages(latitude, longitude),
    ]);

    return {
      satelliteImageUrl: `data:image/png;base64,${fs.readFileSync(satelliteImagePath).toString('base64')}`,
      streetViewUrls: streetViewPaths.map(path => `data:image/png;base64,${fs.readFileSync(path).toString('base64')}`),
      localPaths: {
        satellite: satelliteImagePath,
        streetViews: streetViewPaths,
      },
    };
  }
}
