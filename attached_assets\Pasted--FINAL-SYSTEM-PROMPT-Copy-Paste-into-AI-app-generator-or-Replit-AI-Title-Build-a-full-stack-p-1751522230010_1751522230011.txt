✅ FINAL SYSTEM PROMPT (Copy & Paste into AI app generator or Replit AI)

Title: Build a full-stack production-grade web app called RoofSnap for roof suitability and solar panel potential analysis.

🔧 Key Requirements:

📁 Project Structure:

Create two main folders:

frontend/ — built in React + Tailwind

backend/ — built with FastAPI + Python (ML + APIs + report generation)

🌐 Frontend (in /frontend):

Built in React + TailwindCSS

Clean, responsive UI with tabs:

Input: Address search bar + “Analyze” button

Map View: Satellite image with color-coded solar panel zones

Street Views: Multi-angle views (front, back, sides)

Summary: NLP-generated overview with kWh, area, panel count

Download: Buttons to export PDF/CSV

Include sample address buttons for demo

Use loading spinners and progress tracking

Communicate with backend via REST API (/analyze, /report, etc.)

🔧 Backend (in /backend):

Use FastAPI

Include routes for:

/geocode: Accepts address and returns lat/long

/fetch_images: Gets satellite and street-view images using Google Static Maps + Street View API

/analyze: Accepts address or image, runs full AI pipeline

/summary: Sends image + data to Gemini 1.5 Flash API for NLP-based summary generation

/report: Generates a professional PDF and downloadable CSV

🧠 AI & ML Integration:

Use YOLOv8 to detect roof area from satellite image

Use Segment Anything (SAM) for accurate roof masking

Use OpenCV to:

Divide roof into solar panel-sized tiles (1.6x1m)

Place grid-aligned rectangles avoiding shaded/obstructed zones

Use color-coded heatmap overlay based on predicted solar potential (Green = high energy, Red = low)

Use PV estimation logic (based on area, tilt, latitude, shading) to estimate:

Total kWh/year

Number of usable panels

Energy efficiency class

📈 Elevation + Shadowing Support:

Use Google Elevation API (or NASA SRTM DEM fallback)

Calculate pitch angle, shadow likelihood

Factor into solar prediction model

🧾 PDF + CSV Report Generation:

PDF should include:

Aligned roof image with overlay

Panel layout grid

Total panels, kWh/year, efficiency grade

Gemini-generated summary & recommendations

CSV should include:

Panel coordinates

Estimated solar output per tile

Shading flags

Raw address and geo-coordinates

🔐 API Keys & Config:

Accept environment variables:

GOOGLE_API_KEY

GEMINI_API_KEY

Store downloaded images, results, and reports in /data/ folder

Set up .env handling in both frontend and backend

⚙️ Bonus:

Deployable via Docker (Dockerfiles for both frontend and backend)

Local image caching to avoid repeated API hits

Use FastAPI’s built-in docs at /docs for testing routes

🎯 Final Goal:

Deliver a working full-stack app (not mock, real) that:

Takes real addresses

Fetches real imagery

Runs actual image analysis

Outputs aligned heatmaps + summaries

Generates professional-grade downloadable reports for solar companies (B2B use)

