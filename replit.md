# RoofSnap - Solar Analysis Platform

## Overview

RoofSnap is a full-stack web application designed to analyze roof suitability and solar panel potential for residential and commercial properties. The platform combines AI-powered roof detection, solar calculations, and comprehensive reporting to provide users with detailed insights about their property's solar energy potential.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: TanStack Query for server state management
- **Routing**: Wouter for client-side routing
- **Build Tool**: Vite for development and production builds

### Backend Architecture
- **Runtime**: Node.js with Express server
- **Language**: TypeScript (ESNext modules)
- **Database**: PostgreSQL with Drizzle ORM
- **Database Provider**: Neon Database (@neondatabase/serverless)
- **API Style**: RESTful API with structured service layer

### Project Structure
```
├── client/          # React frontend
├── server/          # Express backend
├── shared/          # Shared TypeScript types and schemas
├── migrations/      # Database migrations
└── data/           # File storage for images and reports
```

## Key Components

### Frontend Components
- **AddressInput**: Main entry point for property analysis
- **AnalysisTabs**: Tabbed interface for viewing results (Map, Street Views, Summary, Download)
- **ProgressTracker**: Real-time progress indicator during analysis
- **MapView**: Interactive satellite view with solar panel zone overlays
- **StreetViews**: Multi-angle street view imagery
- **Summary**: AI-generated insights and recommendations
- **RecentAnalyses**: Dashboard for viewing past analyses

### Backend Services
- **GeocodingService**: Google Maps API integration for address resolution
- **ImageryService**: Satellite and street view image fetching
- **AIAnalysisService**: Mock AI analysis for roof detection (YOLOv8/SAM simulation)
- **SolarCalculationsService**: Solar potential calculations and panel placement
- **GeminiService**: AI-powered summary generation using Google's Gemini API
- **ReportGeneratorService**: PDF and CSV report generation

### Data Models
- **Analysis**: Core entity storing analysis results, progress, and metadata
- **User**: User management (prepared for future authentication)

## Data Flow

1. **Address Input**: User enters property address
2. **Geocoding**: Address converted to coordinates via Google Maps API
3. **Image Fetching**: Satellite and street view images retrieved
4. **AI Analysis**: Roof detection and solar zone identification (currently simulated)
5. **Solar Calculations**: Panel count, energy output, and efficiency calculations
6. **AI Summary**: Gemini API generates human-readable insights
7. **Report Generation**: PDF/CSV reports created for download
8. **Result Display**: Interactive visualization with tabbed interface

## External Dependencies

### APIs and Services
- **Google Maps API**: Geocoding, Static Maps, Street View
- **Google AI (Gemini)**: Natural language summary generation
- **Neon Database**: PostgreSQL hosting

### Key Libraries
- **Frontend**: React, TanStack Query, Tailwind CSS, Radix UI, Wouter
- **Backend**: Express, Drizzle ORM, Zod validation
- **Development**: Vite, TypeScript, ESLint

## Deployment Strategy

### Development
- Vite dev server for frontend with HMR
- Node.js/Express server with TypeScript compilation
- Environment-based configuration
- Replit-optimized development setup

### Production
- Vite build process for optimized frontend bundle
- ESBuild for server-side bundling
- Static file serving for built assets
- Database migrations via Drizzle Kit

### Environment Configuration
- `DATABASE_URL`: PostgreSQL connection string
- `GOOGLE_API_KEY`: Google Maps and Services API key
- `GEMINI_API_KEY`: Google AI API key for summary generation

## User Preferences

Preferred communication style: Simple, everyday language.

## Changelog

Changelog:
- July 03, 2025. Initial setup