"""
Computer Vision services for roof detection and solar panel placement
"""
import cv2
import numpy as np
import math
import logging
from typing import List, Dict, Any, Tuple
from shapely.geometry import Point, Polygon, box
from shapely.ops import unary_union
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Polygon as MplPolygon
import tempfile
import base64
import io
from PIL import Image

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class RoofDetector:
    """Computer Vision-based roof detection using edge detection and contour analysis"""
    
    def __init__(self):
        self.min_roof_area = 50  # minimum roof area in square meters
        
    def detect_roof(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Detect roof boundaries in satellite image using computer vision
        Returns roof polygon and metadata
        """
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Edge detection using Canny
        edges = cv2.Canny(blurred, 50, 150)
        
        # Morphological operations to connect edges
        kernel = np.ones((3, 3), np.uint8)
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        edges = cv2.dilate(edges, kernel, iterations=1)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Filter contours to find roof-like shapes
        roof_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 1000:  # Filter small contours
                # Approximate polygon
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Check if it's a reasonable polygon for a roof
                if len(approx) >= 4 and len(approx) <= 12:
                    roof_contours.append(approx)
        
        if not roof_contours:
            # Fallback: create a central rectangular area
            h, w = image.shape[:2]
            center_x, center_y = w // 2, h // 2
            roof_w, roof_h = w // 3, h // 3
            
            roof_polygon = [
                [center_x - roof_w//2, center_y - roof_h//2],
                [center_x + roof_w//2, center_y - roof_h//2],
                [center_x + roof_w//2, center_y + roof_h//2],
                [center_x - roof_w//2, center_y + roof_h//2]
            ]
        else:
            # Use the largest contour as the main roof
            largest_contour = max(roof_contours, key=cv2.contourArea)
            roof_polygon = largest_contour.reshape(-1, 2).tolist()
        
        return {
            "roof_polygon": roof_polygon,
            "confidence": 0.85,
            "area_pixels": cv2.contourArea(np.array(roof_polygon, dtype=np.int32)),
            "detected_contours": len(roof_contours)
        }


class SolarPanelPlacer:
    """Generate optimal solar panel placement on detected roof"""
    
    def __init__(self):
        self.panel_spacing = 0.3  # meters between panels
        self.panel_width = settings.panel_width  # meters
        self.panel_height = settings.panel_height  # meters
        self.panel_area = self.panel_width * self.panel_height  # square meters
        
    def pixel_to_meters(self, pixel_distance: float, zoom_level: int = 19) -> float:
        """Convert pixel distance to meters based on zoom level"""
        # At zoom 19, approximately 0.3 meters per pixel
        meters_per_pixel = 156543.03392 * math.cos(math.radians(37.4419)) / (2 ** zoom_level)
        return pixel_distance * meters_per_pixel
    
    def meters_to_pixels(self, meters: float, zoom_level: int = 19) -> float:
        """Convert meters to pixels based on zoom level"""
        meters_per_pixel = 156543.03392 * math.cos(math.radians(37.4419)) / (2 ** zoom_level)
        return meters / meters_per_pixel
    
    def generate_solar_grid(
        self, 
        roof_polygon: List[List[float]], 
        image_shape: tuple, 
        zoom_level: int = 19
    ) -> Dict[str, Any]:
        """
        Generate solar panel grid within roof boundaries
        """
        # Convert roof polygon to Shapely polygon
        roof_shape = Polygon(roof_polygon)
        
        # Get bounding box
        min_x, min_y, max_x, max_y = roof_shape.bounds
        
        # Convert panel dimensions to pixels
        panel_width_px = self.meters_to_pixels(self.panel_width, zoom_level)
        panel_height_px = self.meters_to_pixels(self.panel_height, zoom_level)
        spacing_px = self.meters_to_pixels(self.panel_spacing, zoom_level)
        
        # Generate grid
        panels = []
        y = min_y
        while y + panel_height_px <= max_y:
            x = min_x
            while x + panel_width_px <= max_x:
                # Create panel rectangle
                panel_rect = box(x, y, x + panel_width_px, y + panel_height_px)
                
                # Check if panel is within roof bounds
                if roof_shape.contains(panel_rect):
                    # Calculate solar potential based on position
                    center_x = x + panel_width_px / 2
                    center_y = y + panel_height_px / 2
                    
                    # Simple heuristic: closer to center = better potential
                    roof_center_x = (min_x + max_x) / 2
                    roof_center_y = (min_y + max_y) / 2
                    
                    distance_from_center = math.sqrt(
                        (center_x - roof_center_x)**2 + (center_y - roof_center_y)**2
                    )
                    
                    # Normalize distance to get efficiency score
                    max_distance = math.sqrt((max_x - min_x)**2 + (max_y - min_y)**2) / 2
                    efficiency = max(0.4, 1.0 - (distance_from_center / max_distance))
                    
                    # Determine potential level
                    if efficiency > 0.8:
                        potential = "high"
                    elif efficiency > 0.6:
                        potential = "medium"
                    elif efficiency > 0.4:
                        potential = "low"
                    else:
                        potential = "unusable"
                    
                    panels.append({
                        "x": float(x),
                        "y": float(y),
                        "width": float(panel_width_px),
                        "height": float(panel_height_px),
                        "potential": potential,
                        "efficiency": float(efficiency),
                        "estimated_output": float(efficiency * 300)  # 300W base output
                    })
                
                x += panel_width_px + spacing_px
            y += panel_height_px + spacing_px
        
        return {
            "panels": panels,
            "total_panels": len(panels),
            "total_output": sum(p["estimated_output"] for p in panels),
            "roof_area_m2": self.pixel_to_meters(roof_shape.area, zoom_level),
            "panel_coverage": len(panels) * self.panel_area
        }


class SolarCalculator:
    """Calculate solar potential and generate reports"""
    
    def calculate_solar_potential(
        self, 
        panels: List[Dict], 
        latitude: float, 
        longitude: float
    ) -> Dict[str, Any]:
        """Calculate comprehensive solar potential analysis"""
        
        if not panels:
            return {
                "total_panels": 0,
                "annual_kwh": 0,
                "total_output": 0,
                "efficiency": 0,
                "grade": "F",
                "panel_breakdown": {
                    "high": 0,
                    "medium": 0,
                    "low": 0
                }
            }
        
        total_panels = len(panels)
        total_output = sum(p["estimated_output"] for p in panels)
        
        # Estimate annual kWh based on location (simplified)
        # Using average of 4.5 peak sun hours for calculation
        peak_sun_hours = 4.5
        annual_kwh = (total_output / 1000) * peak_sun_hours * 365
        
        # Calculate roof statistics
        high_potential = len([p for p in panels if p["potential"] == "high"])
        medium_potential = len([p for p in panels if p["potential"] == "medium"])
        low_potential = len([p for p in panels if p["potential"] == "low"])
        
        # Determine overall grade
        if total_panels == 0:
            grade = "F"
        elif high_potential / total_panels > 0.7:
            grade = "A"
        elif high_potential / total_panels > 0.5:
            grade = "B"
        elif medium_potential / total_panels > 0.5:
            grade = "C"
        else:
            grade = "D"
        
        return {
            "total_panels": total_panels,
            "annual_kwh": round(annual_kwh, 0),
            "total_output": round(total_output, 0),
            "efficiency": round(sum(p["efficiency"] for p in panels) / total_panels, 2) if total_panels > 0 else 0,
            "grade": grade,
            "panel_breakdown": {
                "high": high_potential,
                "medium": medium_potential,
                "low": low_potential
            }
        }


class VisionService:
    """Main service class that orchestrates all computer vision operations"""

    def __init__(self):
        self.roof_detector = RoofDetector()
        self.panel_placer = SolarPanelPlacer()
        self.solar_calculator = SolarCalculator()

    async def analyze_roof_from_image(
        self,
        image_data: bytes,
        latitude: float,
        longitude: float,
        zoom: int = 19
    ) -> Dict[str, Any]:
        """
        Complete roof analysis from image data

        Args:
            image_data: Raw image bytes
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            zoom: Zoom level for pixel-to-meter conversion

        Returns:
            Complete analysis results
        """
        try:
            # Convert bytes to numpy array
            image_array = np.array(Image.open(io.BytesIO(image_data)))

            # Detect roof
            roof_result = self.roof_detector.detect_roof(image_array)

            # Generate solar panel layout
            solar_grid = self.panel_placer.generate_solar_grid(
                roof_result["roof_polygon"],
                image_array.shape,
                zoom
            )

            # Calculate solar potential
            solar_analysis = self.solar_calculator.calculate_solar_potential(
                solar_grid["panels"],
                latitude,
                longitude
            )

            # Generate visualization
            visualization = self.generate_panel_visualization(
                image_array,
                roof_result["roof_polygon"],
                solar_grid["panels"]
            )

            return {
                "roof_detection": roof_result,
                "solar_grid": solar_grid,
                "solar_analysis": solar_analysis,
                "visualization": visualization,
                "success": True
            }

        except Exception as e:
            logger.error(f"Error in roof analysis: {e}")
            return {
                "error": str(e),
                "success": False
            }

    def generate_panel_visualization(
        self,
        image: np.ndarray,
        roof_polygon: List[List[float]],
        panels: List[Dict[str, Any]]
    ) -> str:
        """
        Generate visualization of solar panels on roof
        Returns base64 encoded image
        """
        try:
            # Create figure
            fig, ax = plt.subplots(1, 1, figsize=(12, 8))

            # Display original image
            ax.imshow(image)

            # Draw roof outline
            if roof_polygon:
                roof_patch = MplPolygon(roof_polygon, linewidth=2, edgecolor='yellow',
                                      facecolor='none', alpha=0.8)
                ax.add_patch(roof_patch)

            # Draw solar panels with color coding
            color_map = {
                'high': 'green',
                'medium': 'orange',
                'low': 'red',
                'unusable': 'gray'
            }

            for panel in panels:
                color = color_map.get(panel['potential'], 'gray')
                rect = patches.Rectangle(
                    (panel['x'], panel['y']),
                    panel['width'],
                    panel['height'],
                    linewidth=1,
                    edgecolor='white',
                    facecolor=color,
                    alpha=0.7
                )
                ax.add_patch(rect)

            # Add legend
            legend_elements = [
                patches.Patch(color='green', label='High Potential'),
                patches.Patch(color='orange', label='Medium Potential'),
                patches.Patch(color='red', label='Low Potential'),
                patches.Patch(color='gray', label='Unusable')
            ]
            ax.legend(handles=legend_elements, loc='upper right')

            ax.set_title('Solar Panel Layout Analysis')
            ax.axis('off')

            # Save to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', bbox_inches='tight', dpi=150)
            buffer.seek(0)

            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            plt.close(fig)

            return f"data:image/png;base64,{image_base64}"

        except Exception as e:
            logger.error(f"Error generating visualization: {e}")
            return ""
