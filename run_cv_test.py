#!/usr/bin/env python3
"""
Simple test runner for the CV backend
"""
import sys
import os
import subprocess
import time

# Start the Python CV backend
print("Starting Python CV Backend...")
try:
    # Change to the python_backend directory and run the server
    os.chdir('python_backend')
    proc = subprocess.Popen([
        sys.executable, '-m', 'uvicorn', 
        'main:app', 
        '--host', '0.0.0.0', 
        '--port', '8000'
    ])
    
    # Wait a bit for server to start
    time.sleep(3)
    
    # Test the server
    os.system('curl -X GET http://localhost:8000/health')
    
    # Keep the server running
    proc.wait()
    
except KeyboardInterrupt:
    print("\nShutting down CV backend...")
    proc.terminate()
except Exception as e:
    print(f"Error starting CV backend: {e}")
    if 'proc' in locals():
        proc.terminate()