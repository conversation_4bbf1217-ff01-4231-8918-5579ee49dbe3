import { <PERSON>, <PERSON>ader<PERSON>, <PERSON> } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import type { Analysis } from "@shared/schema";

interface ProgressTrackerProps {
  analysis: Analysis | undefined;
}

export default function ProgressTracker({ analysis }: ProgressTrackerProps) {
  if (!analysis) return null;

  const steps = [
    {
      id: 'geocoding',
      title: 'Geocoding Address',
      description: 'Converting address to coordinates',
      icon: Check,
      completed: !!analysis.latitude && !!analysis.longitude,
    },
    {
      id: 'images',
      title: 'Fetching Satellite Images',
      description: 'Downloading high-resolution imagery',
      icon: Loader2,
      completed: !!analysis.satelliteImageUrl,
    },
    {
      id: 'analysis',
      title: 'AI Roof Detection',
      description: 'Running YOLOv8 and SAM models',
      icon: Brain,
      completed: !!analysis.roofArea,
    },
    {
      id: 'calculations',
      title: 'Solar Calculations',
      description: 'Computing energy potential',
      icon: Check,
      completed: !!analysis.annualKwh,
    },
    {
      id: 'summary',
      title: 'Generating Summary',
      description: 'Creating AI-powered insights',
      icon: Check,
      completed: !!analysis.aiSummary,
    },
  ];

  const currentStep = steps.findIndex(step => !step.completed);
  const isAnalysisCompleted = analysis.status === 'completed';

  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="text-lg">Analysis Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = index === currentStep && !isAnalysisCompleted;
            const isStepCompleted = step.completed;
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  isStepCompleted 
                    ? 'bg-accent text-white' 
                    : isActive 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-300 text-gray-600'
                }`}>
                  <Icon className={`h-4 w-4 ${isActive ? 'animate-spin' : ''}`} />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-900">{step.title}</p>
                  <p className="text-sm text-gray-500">{step.description}</p>
                </div>
              </div>
            );
          })}
        </div>
        
        {analysis.status === 'failed' && (
          <div className="mt-4 p-3 bg-destructive/10 text-destructive rounded-lg">
            <p className="text-sm font-medium">Analysis Failed</p>
            <p className="text-sm">Please try again or contact support if the problem persists.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
