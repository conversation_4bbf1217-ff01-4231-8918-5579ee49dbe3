#!/usr/bin/env python3
"""
Test the CV backend functionality
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'python_backend'))

try:
    from main import app
    print("✓ FastAPI app imported successfully")
    
    # Test the CV classes
    from main import RoofDetector, SolarPanelPlacer, SolarCalculator
    
    roof_detector = RoofDetector()
    panel_placer = SolarPanelPlacer()
    solar_calculator = SolarCalculator()
    
    print("✓ CV classes instantiated successfully")
    
    # Test a simple roof detection on a dummy image
    import numpy as np
    dummy_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
    
    roof_result = roof_detector.detect_roof(dummy_image)
    print(f"✓ Roof detection working: {len(roof_result['roof_polygon'])} points detected")
    
    # Test solar panel placement
    solar_grid = panel_placer.generate_solar_grid(roof_result['roof_polygon'], dummy_image.shape)
    print(f"✓ Solar panel placement working: {solar_grid['total_panels']} panels generated")
    
    # Test solar calculations
    solar_analysis = solar_calculator.calculate_solar_potential(solar_grid['panels'], 37.4419, -122.1430)
    print(f"✓ Solar analysis working: Grade {solar_analysis['grade']}, {solar_analysis['annual_kwh']} kWh/year")
    
    print("\n🎉 All CV backend components are working!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()