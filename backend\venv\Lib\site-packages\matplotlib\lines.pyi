from .artist import Artist
from .axes import Axes
from .backend_bases import <PERSON><PERSON><PERSON>, FigureCanvasBase
from .path import Path
from .transforms import Bbox

from collections.abc import Callable, Sequence
from typing import Any, Literal, overload
from .typing import (
    ColorType,
    DrawStyleType,
    FillStyleType,
    LineStyleType,
    CapStyleType,
    JoinStyleType,
    MarkEveryType,
    MarkerType,
)
from numpy.typing import Array<PERSON>ike

def segment_hits(
    cx: ArrayLike, cy: ArrayLike, x: ArrayLike, y: ArrayLike, radius: ArrayLike
) -> ArrayLike: ...

class Line2D(Artist):
    lineStyles: dict[str, str]
    drawStyles: dict[str, str]
    drawStyleKeys: list[str]
    markers: dict[str | int, str]
    filled_markers: tuple[str, ...]
    fillStyles: tuple[str, ...]
    zorder: float
    ind_offset: float
    def __init__(
        self,
        xdata: Array<PERSON>ike,
        ydata: <PERSON><PERSON><PERSON><PERSON>ike,
        *,
        linewidth: float | None = ...,
        linestyle: LineStyleType | None = ...,
        color: ColorType | None = ...,
        gapcolor: ColorType | None = ...,
        marker: MarkerType | None = ...,
        markersize: float | None = ...,
        markeredgewidth: float | None = ...,
        markeredgecolor: ColorType | None = ...,
        markerfacecolor: ColorType | None = ...,
        markerfacecoloralt: ColorType = ...,
        fillstyle: FillStyleType | None = ...,
        antialiased: bool | None = ...,
        dash_capstyle: CapStyleType | None = ...,
        solid_capstyle: CapStyleType | None = ...,
        dash_joinstyle: JoinStyleType | None = ...,
        solid_joinstyle: JoinStyleType | None = ...,
        pickradius: float = ...,
        drawstyle: DrawStyleType | None = ...,
        markevery: MarkEveryType | None = ...,
        **kwargs
    ) -> None: ...
    def contains(self, mouseevent: MouseEvent) -> tuple[bool, dict]: ...
    def get_pickradius(self) -> float: ...
    def set_pickradius(self, pickradius: float) -> None: ...
    pickradius: float
    def get_fillstyle(self) -> FillStyleType: ...
    stale: bool
    def set_fillstyle(self, fs: FillStyleType) -> None: ...
    def set_markevery(self, every: MarkEveryType) -> None: ...
    def get_markevery(self) -> MarkEveryType: ...
    def set_picker(
        self, p: None | bool | float | Callable[[Artist, MouseEvent], tuple[bool, dict]]
    ) -> None: ...
    def get_bbox(self) -> Bbox: ...
    @overload
    def set_data(self, args: ArrayLike) -> None: ...
    @overload
    def set_data(self, x: ArrayLike, y: ArrayLike) -> None: ...
    def recache_always(self) -> None: ...
    def recache(self, always: bool = ...) -> None: ...
    def get_antialiased(self) -> bool: ...
    def get_color(self) -> ColorType: ...
    def get_drawstyle(self) -> DrawStyleType: ...
    def get_gapcolor(self) -> ColorType: ...
    def get_linestyle(self) -> LineStyleType: ...
    def get_linewidth(self) -> float: ...
    def get_marker(self) -> MarkerType: ...
    def get_markeredgecolor(self) -> ColorType: ...
    def get_markeredgewidth(self) -> float: ...
    def get_markerfacecolor(self) -> ColorType: ...
    def get_markerfacecoloralt(self) -> ColorType: ...
    def get_markersize(self) -> float: ...
    def get_data(self, orig: bool = ...) -> tuple[ArrayLike, ArrayLike]: ...
    def get_xdata(self, orig: bool = ...) -> ArrayLike: ...
    def get_ydata(self, orig: bool = ...) -> ArrayLike: ...
    def get_path(self) -> Path: ...
    def get_xydata(self) -> ArrayLike: ...
    def set_antialiased(self, b: bool) -> None: ...
    def set_color(self, color: ColorType) -> None: ...
    def set_drawstyle(self, drawstyle: DrawStyleType | None) -> None: ...
    def set_gapcolor(self, gapcolor: ColorType | None) -> None: ...
    def set_linewidth(self, w: float) -> None: ...
    def set_linestyle(self, ls: LineStyleType) -> None: ...
    def set_marker(self, marker: MarkerType) -> None: ...
    def set_markeredgecolor(self, ec: ColorType | None) -> None: ...
    def set_markerfacecolor(self, fc: ColorType | None) -> None: ...
    def set_markerfacecoloralt(self, fc: ColorType | None) -> None: ...
    def set_markeredgewidth(self, ew: float | None) -> None: ...
    def set_markersize(self, sz: float) -> None: ...
    def set_xdata(self, x: ArrayLike) -> None: ...
    def set_ydata(self, y: ArrayLike) -> None: ...
    def set_dashes(self, seq: Sequence[float] | tuple[None, None]) -> None: ...
    def update_from(self, other: Artist) -> None: ...
    def set_dash_joinstyle(self, s: JoinStyleType) -> None: ...
    def set_solid_joinstyle(self, s: JoinStyleType) -> None: ...
    def get_dash_joinstyle(self) -> Literal["miter", "round", "bevel"]: ...
    def get_solid_joinstyle(self) -> Literal["miter", "round", "bevel"]: ...
    def set_dash_capstyle(self, s: CapStyleType) -> None: ...
    def set_solid_capstyle(self, s: CapStyleType) -> None: ...
    def get_dash_capstyle(self) -> Literal["butt", "projecting", "round"]: ...
    def get_solid_capstyle(self) -> Literal["butt", "projecting", "round"]: ...
    def is_dashed(self) -> bool: ...

class AxLine(Line2D):
    def __init__(
        self,
        xy1: tuple[float, float],
        xy2: tuple[float, float] | None,
        slope: float | None,
        **kwargs
    ) -> None: ...
    def get_xy1(self) -> tuple[float, float] | None: ...
    def get_xy2(self) -> tuple[float, float] | None: ...
    def get_slope(self) -> float: ...
    def set_xy1(self, xy1: tuple[float, float]) -> None: ...
    def set_xy2(self, xy2: tuple[float, float]) -> None: ...
    def set_slope(self, slope: float) -> None: ...

class VertexSelector:
    axes: Axes
    line: Line2D
    cid: int
    ind: set[int]
    def __init__(self, line: Line2D) -> None: ...
    @property
    def canvas(self) -> FigureCanvasBase: ...
    def process_selected(
        self, ind: Sequence[int], xs: ArrayLike, ys: ArrayLike
    ) -> None: ...
    def onpick(self, event: Any) -> None: ...

lineStyles: dict[str, str]
lineMarkers: dict[str | int, str]
drawStyles: dict[str, str]
fillStyles: tuple[FillStyleType, ...]
