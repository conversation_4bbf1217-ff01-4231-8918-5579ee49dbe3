#!/usr/bin/env python3
"""
Comprehensive system test for RoofSnap refactored backend
"""
import asyncio
import aiohttp
import json
import time
import os
import sys
from typing import Dict, Any

# Test configuration
BACKEND_URL = "http://localhost:8000"
TEST_ADDRESS = "1600 Amphitheatre Parkway, Mountain View, CA"


class RoofSnapTester:
    """Test suite for RoofSnap backend"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url
        self.session = None
        self.test_results = []
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def log_test(self, test_name: str, success: bool, message: str = "", data: Any = None):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "data": data
        })
    
    async def test_health_check(self):
        """Test basic health endpoint"""
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    self.log_test("Health Check", True, f"Status: {data.get('status')}")
                    return data
                else:
                    self.log_test("Health Check", False, f"HTTP {response.status}")
                    return None
        except Exception as e:
            self.log_test("Health Check", False, f"Connection error: {e}")
            return None
    
    async def test_geocoding(self):
        """Test geocoding service"""
        try:
            payload = {"address": TEST_ADDRESS}
            async with self.session.post(f"{self.base_url}/api/geocode", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    lat = data.get("latitude")
                    lng = data.get("longitude")
                    self.log_test("Geocoding", True, f"Coordinates: {lat}, {lng}")
                    return data
                else:
                    error_text = await response.text()
                    self.log_test("Geocoding", False, f"HTTP {response.status}: {error_text}")
                    return None
        except Exception as e:
            self.log_test("Geocoding", False, f"Error: {e}")
            return None
    
    async def test_analysis_start(self):
        """Test starting a new analysis"""
        try:
            payload = {"address": TEST_ADDRESS}
            async with self.session.post(f"{self.base_url}/api/analyze", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    analysis_id = data.get("analysisId")
                    self.log_test("Start Analysis", True, f"Analysis ID: {analysis_id}")
                    return analysis_id
                else:
                    error_text = await response.text()
                    self.log_test("Start Analysis", False, f"HTTP {response.status}: {error_text}")
                    return None
        except Exception as e:
            self.log_test("Start Analysis", False, f"Error: {e}")
            return None
    
    async def test_analysis_status(self, analysis_id: int, max_wait: int = 60):
        """Test analysis status polling"""
        try:
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                async with self.session.get(f"{self.base_url}/api/analyses/{analysis_id}") as response:
                    if response.status == 200:
                        data = await response.json()
                        status = data.get("status")
                        
                        if status == "completed":
                            self.log_test("Analysis Completion", True, f"Completed in {time.time() - start_time:.1f}s")
                            return data
                        elif status == "failed":
                            self.log_test("Analysis Completion", False, "Analysis failed")
                            return data
                        else:
                            print(f"⏳ Analysis status: {status}")
                            await asyncio.sleep(2)
                    else:
                        self.log_test("Analysis Status Check", False, f"HTTP {response.status}")
                        return None
            
            self.log_test("Analysis Completion", False, f"Timeout after {max_wait}s")
            return None
            
        except Exception as e:
            self.log_test("Analysis Status Check", False, f"Error: {e}")
            return None
    
    async def test_roof_analysis_endpoints(self):
        """Test specialized roof analysis endpoints"""
        try:
            # Test detection methods endpoint
            async with self.session.get(f"{self.base_url}/api/roof/detection-methods") as response:
                if response.status == 200:
                    data = await response.json()
                    methods = data.get("methods", [])
                    self.log_test("Roof Detection Methods", True, f"Found {len(methods)} methods")
                else:
                    self.log_test("Roof Detection Methods", False, f"HTTP {response.status}")
            
            # Test coordinate analysis
            payload = {
                "latitude": 37.4220,
                "longitude": -122.0841,
                "address": TEST_ADDRESS
            }
            async with self.session.post(f"{self.base_url}/api/roof/analyze-coordinates", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    success = data.get("success", False)
                    self.log_test("Roof Coordinate Analysis", success, "CV analysis completed")
                else:
                    error_text = await response.text()
                    self.log_test("Roof Coordinate Analysis", False, f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            self.log_test("Roof Analysis Endpoints", False, f"Error: {e}")
    
    async def test_report_generation(self, analysis_id: int):
        """Test PDF and CSV report generation"""
        try:
            # Test PDF generation
            async with self.session.get(f"{self.base_url}/api/analyses/{analysis_id}/report/pdf") as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'pdf' in content_type:
                        self.log_test("PDF Report", True, "PDF generated successfully")
                    else:
                        self.log_test("PDF Report", False, f"Wrong content type: {content_type}")
                else:
                    self.log_test("PDF Report", False, f"HTTP {response.status}")
            
            # Test CSV generation
            async with self.session.get(f"{self.base_url}/api/analyses/{analysis_id}/report/csv") as response:
                if response.status == 200:
                    content_type = response.headers.get('content-type', '')
                    if 'csv' in content_type:
                        self.log_test("CSV Report", True, "CSV generated successfully")
                    else:
                        self.log_test("CSV Report", False, f"Wrong content type: {content_type}")
                else:
                    self.log_test("CSV Report", False, f"HTTP {response.status}")
                    
        except Exception as e:
            self.log_test("Report Generation", False, f"Error: {e}")
    
    async def test_list_analyses(self):
        """Test listing analyses"""
        try:
            async with self.session.get(f"{self.base_url}/api/analyses") as response:
                if response.status == 200:
                    data = await response.json()
                    count = len(data) if isinstance(data, list) else 0
                    self.log_test("List Analyses", True, f"Found {count} analyses")
                    return data
                else:
                    self.log_test("List Analyses", False, f"HTTP {response.status}")
                    return None
        except Exception as e:
            self.log_test("List Analyses", False, f"Error: {e}")
            return None
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("="*60)


async def main():
    """Run comprehensive system test"""
    print("🚀 Starting RoofSnap System Test")
    print(f"Backend URL: {BACKEND_URL}")
    print(f"Test Address: {TEST_ADDRESS}")
    print("-" * 60)
    
    async with RoofSnapTester(BACKEND_URL) as tester:
        # Basic connectivity tests
        health_data = await tester.test_health_check()
        if not health_data:
            print("❌ Backend is not responding. Please start the backend first.")
            return
        
        # API functionality tests
        await tester.test_geocoding()
        await tester.test_roof_analysis_endpoints()
        await tester.test_list_analyses()
        
        # Full analysis workflow test
        analysis_id = await tester.test_analysis_start()
        if analysis_id:
            analysis_data = await tester.test_analysis_status(analysis_id, max_wait=120)
            if analysis_data and analysis_data.get("status") == "completed":
                await tester.test_report_generation(analysis_id)
        
        # Print results
        tester.print_summary()


if __name__ == "__main__":
    print("RoofSnap System Tester")
    print("Make sure the backend is running on http://localhost:8000")
    print("Press Ctrl+C to cancel\n")
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Test cancelled by user")
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        sys.exit(1)
