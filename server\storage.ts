import { analyses, users, type Analysis, type InsertAnalysis, type UpdateAnalysis, type User, type InsertUser } from "@shared/schema";

export interface IStorage {
  // User methods (keeping for compatibility)
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Analysis methods
  getAnalysis(id: number): Promise<Analysis | undefined>;
  getAnalysesByAddress(address: string): Promise<Analysis[]>;
  getRecentAnalyses(limit?: number): Promise<Analysis[]>;
  createAnalysis(analysis: InsertAnalysis): Promise<Analysis>;
  updateAnalysis(id: number, analysis: UpdateAnalysis): Promise<Analysis | undefined>;
  deleteAnalysis(id: number): Promise<boolean>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private analyses: Map<number, Analysis>;
  private currentUserId: number;
  private currentAnalysisId: number;

  constructor() {
    this.users = new Map();
    this.analyses = new Map();
    this.currentUserId = 1;
    this.currentAnalysisId = 1;
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Analysis methods
  async getAnalysis(id: number): Promise<Analysis | undefined> {
    return this.analyses.get(id);
  }

  async getAnalysesByAddress(address: string): Promise<Analysis[]> {
    return Array.from(this.analyses.values()).filter(
      (analysis) => analysis.address.toLowerCase().includes(address.toLowerCase())
    );
  }

  async getRecentAnalyses(limit: number = 10): Promise<Analysis[]> {
    return Array.from(this.analyses.values())
      .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())
      .slice(0, limit);
  }

  async createAnalysis(insertAnalysis: InsertAnalysis): Promise<Analysis> {
    const id = this.currentAnalysisId++;
    const now = new Date();
    const analysis: Analysis = {
      ...insertAnalysis,
      id,
      status: insertAnalysis.status || "pending",
      orientation: insertAnalysis.orientation || null,
      satelliteImageUrl: insertAnalysis.satelliteImageUrl || null,
      streetViewUrls: insertAnalysis.streetViewUrls as string[] || null,
      roofArea: insertAnalysis.roofArea || null,
      usableArea: insertAnalysis.usableArea || null,
      panelCount: insertAnalysis.panelCount || null,
      annualKwh: insertAnalysis.annualKwh || null,
      efficiency: insertAnalysis.efficiency || null,
      grade: insertAnalysis.grade || null,
      roofTilt: insertAnalysis.roofTilt || null,
      shadingLevel: insertAnalysis.shadingLevel || null,
      solarGrid: insertAnalysis.solarGrid as any || null,
      pdfReportPath: insertAnalysis.pdfReportPath || null,
      csvReportPath: insertAnalysis.csvReportPath || null,
      aiSummary: insertAnalysis.aiSummary || null,
      recommendations: insertAnalysis.recommendations as string[] || null,
      createdAt: now,
      updatedAt: now,
    };
    this.analyses.set(id, analysis);
    return analysis;
  }

  async updateAnalysis(id: number, updateAnalysis: UpdateAnalysis): Promise<Analysis | undefined> {
    const existing = this.analyses.get(id);
    if (!existing) return undefined;

    const updated: Analysis = {
      ...existing,
      ...updateAnalysis,
      streetViewUrls: updateAnalysis.streetViewUrls as string[] || existing.streetViewUrls,
      solarGrid: updateAnalysis.solarGrid as any || existing.solarGrid,
      recommendations: updateAnalysis.recommendations as string[] || existing.recommendations,
      updatedAt: new Date(),
    };
    this.analyses.set(id, updated);
    return updated;
  }

  async deleteAnalysis(id: number): Promise<boolean> {
    return this.analyses.delete(id);
  }
}

export const storage = new MemStorage();
