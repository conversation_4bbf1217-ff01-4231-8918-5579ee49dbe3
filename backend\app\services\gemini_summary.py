"""
Gemini AI service for generating intelligent summaries and recommendations
"""
import aiohttp
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class GeminiSummaryService:
    """Enhanced Gemini service for comprehensive solar analysis summaries"""
    
    def __init__(self):
        self.api_key = settings.gemini_api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"
        
        if not self.api_key:
            logger.warning("Gemini API key not configured - AI summaries will be disabled")
    
    async def generate_executive_summary(
        self,
        analysis_data: Dict[str, Any],
        address: str
    ) -> str:
        """
        Generate executive summary for property owners and decision makers
        """
        if not self.api_key:
            return "AI summary unavailable - Gemini API key not configured"
        
        prompt = self._create_executive_prompt(analysis_data, address)
        return await self._call_gemini_api(prompt)
    
    async def generate_technical_report(
        self,
        analysis_data: Dict[str, Any],
        address: str
    ) -> str:
        """
        Generate detailed technical report for installers and engineers
        """
        if not self.api_key:
            return "Technical report unavailable - Gemini API key not configured"
        
        prompt = self._create_technical_prompt(analysis_data, address)
        return await self._call_gemini_api(prompt)
    
    async def generate_financial_analysis(
        self,
        analysis_data: Dict[str, Any],
        address: str,
        electricity_rate: float = 0.12,
        system_cost_per_watt: float = 3.50
    ) -> str:
        """
        Generate financial analysis and ROI projections
        """
        if not self.api_key:
            return "Financial analysis unavailable - Gemini API key not configured"
        
        prompt = self._create_financial_prompt(analysis_data, address, electricity_rate, system_cost_per_watt)
        return await self._call_gemini_api(prompt)
    
    async def generate_recommendations(
        self,
        analysis_data: Dict[str, Any],
        address: str
    ) -> List[str]:
        """
        Generate actionable recommendations based on analysis
        """
        if not self.api_key:
            return ["AI recommendations unavailable - Gemini API key not configured"]
        
        prompt = self._create_recommendations_prompt(analysis_data, address)
        response = await self._call_gemini_api(prompt)
        
        # Parse recommendations from response
        recommendations = []
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line and (line.startswith('•') or line.startswith('-') or line.startswith('*')):
                recommendations.append(line.lstrip('•-* '))
            elif line and any(char.isdigit() for char in line[:3]) and '.' in line[:3]:
                recommendations.append(line.split('.', 1)[1].strip())
        
        return recommendations if recommendations else [response]
    
    def _create_executive_prompt(self, analysis_data: Dict[str, Any], address: str) -> str:
        """Create executive summary prompt"""
        solar_analysis = analysis_data.get("solar_analysis", {})
        solar_grid = analysis_data.get("solar_grid", {})
        
        return f"""
        Create a professional executive summary for a solar analysis report for the property at {address}.
        
        Key Metrics:
        - Solar Panels: {solar_analysis.get('total_panels', 0)}
        - Annual Production: {solar_analysis.get('annual_kwh', 0):,.0f} kWh
        - System Capacity: {solar_analysis.get('total_output', 0):,.0f} watts
        - Efficiency Rating: {solar_analysis.get('efficiency', 0):.1%}
        - Solar Grade: {solar_analysis.get('grade', 'N/A')}
        - Roof Area: {solar_grid.get('roof_area_m2', 0):.1f} m²
        
        Panel Quality Distribution:
        - High Efficiency: {solar_analysis.get('panel_breakdown', {}).get('high', 0)} panels
        - Medium Efficiency: {solar_analysis.get('panel_breakdown', {}).get('medium', 0)} panels
        - Low Efficiency: {solar_analysis.get('panel_breakdown', {}).get('low', 0)} panels
        
        Please provide:
        1. A compelling 2-3 sentence executive summary
        2. Key value proposition for solar installation
        3. Primary benefits and expected outcomes
        4. Overall recommendation (Highly Recommended/Recommended/Consider Alternatives/Not Recommended)
        
        Keep it concise, professional, and focused on business value. Target audience: property owner making investment decision.
        """
    
    def _create_technical_prompt(self, analysis_data: Dict[str, Any], address: str) -> str:
        """Create technical report prompt"""
        roof_detection = analysis_data.get("roof_detection", {})
        solar_grid = analysis_data.get("solar_grid", {})
        solar_analysis = analysis_data.get("solar_analysis", {})
        
        return f"""
        Generate a technical analysis report for solar installation at {address}.
        
        Roof Analysis:
        - Detection Confidence: {roof_detection.get('confidence', 0):.1%}
        - Roof Area: {solar_grid.get('roof_area_m2', 0):.1f} m² ({solar_grid.get('roof_area_m2', 0) * 10.764:.1f} sq ft)
        - Usable Area: {solar_grid.get('panel_coverage', 0):.1f} m²
        - Panel Layout: {solar_grid.get('total_panels', 0)} panels in grid configuration
        
        System Specifications:
        - Total Capacity: {solar_analysis.get('total_output', 0):,.0f} W
        - Expected Annual Production: {solar_analysis.get('annual_kwh', 0):,.0f} kWh
        - System Efficiency: {solar_analysis.get('efficiency', 0):.1%}
        - Performance Grade: {solar_analysis.get('grade', 'N/A')}
        
        Please provide:
        1. Technical feasibility assessment
        2. Roof structural considerations
        3. Optimal panel placement strategy
        4. System sizing recommendations
        5. Installation challenges and solutions
        6. Performance optimization opportunities
        
        Target audience: Solar installers and engineers. Include technical details and implementation considerations.
        """
    
    def _create_financial_prompt(
        self, 
        analysis_data: Dict[str, Any], 
        address: str,
        electricity_rate: float,
        system_cost_per_watt: float
    ) -> str:
        """Create financial analysis prompt"""
        solar_analysis = analysis_data.get("solar_analysis", {})
        annual_kwh = solar_analysis.get('annual_kwh', 0)
        total_output = solar_analysis.get('total_output', 0)
        
        # Calculate financial metrics
        annual_savings = annual_kwh * electricity_rate
        system_cost = (total_output / 1000) * system_cost_per_watt * 1000  # Convert to total cost
        payback_years = system_cost / annual_savings if annual_savings > 0 else 0
        
        return f"""
        Create a comprehensive financial analysis for solar installation at {address}.
        
        System Economics:
        - System Size: {total_output / 1000:.1f} kW
        - Estimated System Cost: ${system_cost:,.0f}
        - Annual Energy Production: {annual_kwh:,.0f} kWh
        - Current Electricity Rate: ${electricity_rate:.2f}/kWh
        - Annual Savings: ${annual_savings:,.0f}
        - Simple Payback: {payback_years:.1f} years
        
        Financial Projections:
        - 10-Year Savings: ${annual_savings * 10:,.0f}
        - 25-Year Savings: ${annual_savings * 25:,.0f}
        - Net Present Value (7% discount): ${annual_savings * 12.4:.0f}
        
        Please provide:
        1. Investment summary and ROI analysis
        2. Cash flow projections (5, 10, 25 years)
        3. Break-even analysis
        4. Available incentives and tax benefits
        5. Financing options and recommendations
        6. Risk factors and sensitivity analysis
        
        Include specific dollar amounts and percentages. Target audience: Property owner evaluating investment.
        """
    
    def _create_recommendations_prompt(self, analysis_data: Dict[str, Any], address: str) -> str:
        """Create recommendations prompt"""
        solar_analysis = analysis_data.get("solar_analysis", {})
        grade = solar_analysis.get("grade", "F")
        panel_breakdown = solar_analysis.get("panel_breakdown", {})
        
        return f"""
        Generate specific, actionable recommendations for solar installation at {address}.
        
        Current Analysis:
        - Solar Grade: {grade}
        - Total Panels: {solar_analysis.get('total_panels', 0)}
        - High Efficiency Areas: {panel_breakdown.get('high', 0)} panels
        - Medium Efficiency Areas: {panel_breakdown.get('medium', 0)} panels
        - Low Efficiency Areas: {panel_breakdown.get('low', 0)} panels
        
        Provide 5-8 specific recommendations covering:
        1. Immediate next steps
        2. System optimization opportunities
        3. Roof preparation requirements
        4. Technology and equipment suggestions
        5. Timeline and planning considerations
        6. Professional consultation needs
        
        Format as bullet points. Be specific and actionable. Focus on maximizing solar potential and investment value.
        """
    
    async def _call_gemini_api(self, prompt: str) -> str:
        """Make API call to Gemini"""
        if not self.api_key:
            return "AI service unavailable"
        
        url = f"{self.base_url}?key={self.api_key}"
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 2048,
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status != 200:
                        logger.error(f"Gemini API error: {response.status}")
                        return "AI summary temporarily unavailable"
                    
                    data = await response.json()
                    
                    if not data.get("candidates"):
                        logger.error("No content generated by Gemini")
                        return "AI summary could not be generated"
                    
                    generated_text = data["candidates"][0]["content"]["parts"][0]["text"]
                    return generated_text.strip()
                    
        except Exception as e:
            logger.error(f"Error calling Gemini API: {e}")
            return "AI summary temporarily unavailable"
