import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Search, MapPin, Zap, Clock, Sun } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface AddressInputBarProps {
  onAnalysisStart: (analysisId: number) => void;
}

export default function AddressInputBar({ onAnalysisStart }: AddressInputBarProps) {
  const [address, setAddress] = useState("");
  const { toast } = useToast();

  const analyzeMutation = useMutation({
    mutationFn: async (address: string) => {
      const res = await apiRequest("POST", "/api/analyze", { address });
      return res.json();
    },
    onSuccess: (data) => {
      onAnalysisStart(data.analysisId);
      setAddress("");
      toast({
        title: "Analysis Started",
        description: "Your professional solar analysis is now in progress.",
      });
    },
    onError: (error) => {
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to start analysis",
        variant: "destructive",
      });
    },
  });

  const handleAnalyze = () => {
    if (!address.trim()) {
      toast({
        title: "Address Required",
        description: "Please enter a valid property address to analyze.",
        variant: "destructive",
      });
      return;
    }
    analyzeMutation.mutate(address);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAnalyze();
    }
  };

  const sampleAddresses = [
    { address: "1600 Amphitheatre Parkway, Mountain View, CA", label: "Tech Campus", type: "Commercial" },
    { address: "1 Tesla Road, Austin, TX", label: "Tesla Gigafactory", type: "Industrial" },
    { address: "Apple Park, Cupertino, CA", label: "Apple Park", type: "Corporate" },
    { address: "350 5th Ave, New York, NY", label: "Empire State Building", type: "High-Rise" }
  ];

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Main Input Section */}
      <Card className="bg-card dark:glass shadow-xl border dark:border-white/20 border-border hover:border-primary/50 transition-all duration-500 dark:shimmer">
        <CardContent className="p-8">
          <div className="space-y-6">
            <div className="text-center space-y-3">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <div className="relative">
                  <Sun className="h-8 w-8 text-yellow-500 dark:text-yellow-400 rotate-slow" />
                  <div className="absolute inset-0 bg-yellow-400/20 rounded-full blur-lg pulse-soft"></div>
                </div>
                <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
                  Start Your Solar Analysis
                </h2>
                <div className="relative">
                  <Zap className="h-8 w-8 text-blue-500 dark:text-blue-400" />
                  <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-lg pulse-soft"></div>
                </div>
              </div>
              <p className="text-muted-foreground text-lg">Enter any property address for comprehensive solar potential assessment</p>
            </div>
            
            <div className="flex gap-4">
              <div className="relative flex-1 group">
                <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-primary group-focus-within:text-primary/80 transition-colors" />
                <Input
                  type="text"
                  placeholder="Enter property address (e.g., 123 Main St, City, State)"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="pl-12 pr-4 py-4 text-lg bg-background dark:bg-white/10 border-2 border-input dark:border-white/20 focus:border-primary focus:ring-4 focus:ring-primary/20 rounded-xl transition-all duration-300 text-foreground dark:text-white placeholder-muted-foreground dark:placeholder-gray-400 backdrop-blur-sm"
                  disabled={analyzeMutation.isPending}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-purple-500/10 rounded-xl opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
              </div>
              <Button
                onClick={handleAnalyze}
                disabled={analyzeMutation.isPending || !address.trim()}
                className="px-8 py-4 bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-700 text-primary-foreground font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:hover:scale-100 group"
              >
                {analyzeMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-foreground mr-2"></div>
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Search className="h-5 w-5 mr-2 group-hover:scale-110 transition-transform" />
                    Analyze Roof
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Features Section */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-card dark:glass border dark:border-white/20 border-border hover:border-primary/50 hover:shadow-xl transition-all duration-500 hover:scale-105 group slide-in-up">
          <CardContent className="p-6 text-center">
            <div className="relative w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4 float group-hover:scale-110 transition-transform duration-300">
              <MapPin className="h-7 w-7 text-white" />
              <div className="absolute inset-0 bg-blue-400/20 rounded-xl blur-lg pulse-soft"></div>
            </div>
            <h3 className="font-semibold text-card-foreground mb-2 text-lg">AI-Powered Analysis</h3>
            <p className="text-sm text-muted-foreground">Advanced roof detection and solar zone mapping using satellite imagery</p>
          </CardContent>
        </Card>

        <Card className="bg-card dark:glass border dark:border-white/20 border-border hover:border-green-500/50 hover:shadow-xl transition-all duration-500 hover:scale-105 group slide-in-up" style={{animationDelay: '0.2s'}}>
          <CardContent className="p-6 text-center">
            <div className="relative w-14 h-14 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mx-auto mb-4 float-delayed group-hover:scale-110 transition-transform duration-300">
              <Zap className="h-7 w-7 text-white" />
              <div className="absolute inset-0 bg-green-400/20 rounded-xl blur-lg pulse-soft"></div>
            </div>
            <h3 className="font-semibold text-card-foreground mb-2 text-lg">Energy Estimation</h3>
            <p className="text-sm text-muted-foreground">Precise solar panel placement and annual energy production calculations</p>
          </CardContent>
        </Card>

        <Card className="bg-card dark:glass border dark:border-white/20 border-border hover:border-purple-500/50 hover:shadow-xl transition-all duration-500 hover:scale-105 group slide-in-up" style={{animationDelay: '0.4s'}}>
          <CardContent className="p-6 text-center">
            <div className="relative w-14 h-14 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center mx-auto mb-4 float group-hover:scale-110 transition-transform duration-300">
              <Clock className="h-7 w-7 text-white" />
              <div className="absolute inset-0 bg-purple-400/20 rounded-xl blur-lg pulse-soft"></div>
            </div>
            <h3 className="font-semibold text-card-foreground mb-2 text-lg">Instant Reports</h3>
            <p className="text-sm text-muted-foreground">Professional PDF reports with detailed analysis and recommendations</p>
          </CardContent>
        </Card>
      </div>

      {/* Sample Addresses */}
      <Card className="bg-card dark:glass border dark:border-white/20 border-border hover:border-primary/30 transition-all duration-300">
        <CardContent className="p-6">
          <h3 className="font-medium text-card-foreground mb-4 text-lg flex items-center gap-2">
            <MapPin className="h-5 w-5 text-primary" />
            Try these sample addresses:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {sampleAddresses.map((sample, index) => (
              <button
                key={index}
                onClick={() => setAddress(sample.address)}
                className="text-left p-4 rounded-lg border dark:border-white/20 border-border hover:border-primary/50 hover:bg-accent transition-all duration-300 group dark:shimmer"
                disabled={analyzeMutation.isPending}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-card-foreground group-hover:text-primary transition-colors">
                      {sample.label}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">{sample.address}</p>
                  </div>
                  <Badge variant="secondary" className="text-xs bg-primary/20 text-primary border-primary/30">
                    {sample.type}
                  </Badge>
                </div>
              </button>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}