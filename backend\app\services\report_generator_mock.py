"""
Mock report generation service for localhost testing (without reportlab)
"""
import os
import csv
import logging
from typing import Dict, Any
from datetime import datetime

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class MockReportGeneratorService:
    """Mock report generator for localhost testing"""
    
    def __init__(self):
        self.data_dir = settings.data_directory
        os.makedirs(self.data_dir, exist_ok=True)
    
    async def generate_pdf_report(self, analysis_data: Dict[str, Any]) -> str:
        """
        Generate mock PDF report (creates a text file instead)
        """
        try:
            # Generate filename
            timestamp = int(datetime.now().timestamp() * 1000)
            filename = f"solar_report_{timestamp}.txt"
            filepath = os.path.join(self.data_dir, filename)
            
            # Create text report instead of PDF
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("ROOFSNAP SOLAR ANALYSIS REPORT\n")
                f.write("=" * 40 + "\n\n")
                
                f.write("PROPERTY INFORMATION\n")
                f.write("-" * 20 + "\n")
                f.write(f"Address: {analysis_data.get('address', 'N/A')}\n")
                f.write(f"Coordinates: {analysis_data.get('latitude', 0):.6f}, {analysis_data.get('longitude', 0):.6f}\n")
                f.write(f"Analysis Date: {datetime.now().strftime('%B %d, %Y')}\n")
                f.write(f"Status: {analysis_data.get('status', 'N/A').title()}\n\n")
                
                f.write("SOLAR ANALYSIS RESULTS\n")
                f.write("-" * 22 + "\n")
                f.write(f"Total Solar Panels: {analysis_data.get('panel_count', 0)}\n")
                f.write(f"Annual Energy Production: {analysis_data.get('annual_kwh', 0):,.0f} kWh\n")
                f.write(f"System Efficiency: {analysis_data.get('efficiency', 0):.1%}\n")
                f.write(f"Solar Grade: {analysis_data.get('grade', 'N/A')}\n")
                f.write(f"Roof Area: {analysis_data.get('roof_area', 0):.1f} sq ft\n")
                f.write(f"Usable Area: {analysis_data.get('usable_area', 0):.1f} sq ft\n\n")
                
                f.write("FINANCIAL ESTIMATES\n")
                f.write("-" * 19 + "\n")
                annual_kwh = analysis_data.get("annual_kwh", 0)
                annual_savings = annual_kwh * 0.12
                f.write(f"Estimated Annual Savings: ${annual_savings:,.0f}\n")
                f.write(f"25-Year Savings: ${annual_savings * 25:,.0f}\n")
                f.write(f"Electricity Rate (assumed): $0.12 per kWh\n")
                f.write(f"Payback Period (estimated): 8-12 years\n\n")
                
                ai_summary = analysis_data.get("ai_summary", "")
                if ai_summary:
                    f.write("AI ANALYSIS SUMMARY\n")
                    f.write("-" * 20 + "\n")
                    f.write(f"{ai_summary}\n\n")
                
                recommendations = analysis_data.get("recommendations", [])
                if recommendations:
                    f.write("RECOMMENDATIONS\n")
                    f.write("-" * 15 + "\n")
                    for i, rec in enumerate(recommendations, 1):
                        f.write(f"{i}. {rec}\n")
                    f.write("\n")
                
                f.write("DISCLAIMER\n")
                f.write("-" * 10 + "\n")
                f.write("This report is generated by RoofSnap AI analysis for localhost testing.\n")
                f.write("Consult with certified solar installers for detailed system design.\n")
            
            logger.info(f"Mock PDF report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating mock PDF report: {e}")
            raise
    
    async def generate_csv_report(self, analysis_data: Dict[str, Any]) -> str:
        """
        Generate CSV report for analysis
        """
        try:
            # Generate filename
            timestamp = int(datetime.now().timestamp() * 1000)
            filename = f"solar_data_{timestamp}.csv"
            filepath = os.path.join(self.data_dir, filename)
            
            # Prepare data for CSV
            solar_grid = analysis_data.get("solar_grid", {})
            zones = solar_grid.get("zones", [])
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header information
                writer.writerow(["Solar Analysis Report"])
                writer.writerow(["Address", analysis_data.get("address", "N/A")])
                writer.writerow(["Analysis Date", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
                writer.writerow(["Latitude", analysis_data.get("latitude", 0)])
                writer.writerow(["Longitude", analysis_data.get("longitude", 0)])
                writer.writerow([])
                
                # Write summary data
                writer.writerow(["Summary"])
                writer.writerow(["Total Panels", analysis_data.get("panel_count", 0)])
                writer.writerow(["Annual kWh", analysis_data.get("annual_kwh", 0)])
                writer.writerow(["System Efficiency", analysis_data.get("efficiency", 0)])
                writer.writerow(["Solar Grade", analysis_data.get("grade", "N/A")])
                writer.writerow(["Roof Area (sq ft)", analysis_data.get("roof_area", 0)])
                writer.writerow(["Usable Area (sq ft)", analysis_data.get("usable_area", 0)])
                writer.writerow([])
                
                # Write panel details
                if zones:
                    writer.writerow(["Panel Details"])
                    writer.writerow(["Panel #", "X Position", "Y Position", "Width", "Height", "Potential", "Efficiency", "Estimated Output (W)"])
                    
                    for i, zone in enumerate(zones, 1):
                        writer.writerow([
                            i,
                            zone.get("x", 0),
                            zone.get("y", 0),
                            zone.get("width", 0),
                            zone.get("height", 0),
                            zone.get("potential", "unknown"),
                            zone.get("efficiency", 0),
                            zone.get("estimatedOutput", 0)
                        ])
                
                writer.writerow([])
                
                # Write recommendations
                recommendations = analysis_data.get("recommendations", [])
                if recommendations:
                    writer.writerow(["Recommendations"])
                    for i, rec in enumerate(recommendations, 1):
                        writer.writerow([f"{i}.", rec])
            
            logger.info(f"CSV report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating CSV report: {e}")
            raise
