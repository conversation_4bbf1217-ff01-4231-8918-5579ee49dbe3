#!/bin/bash

# Start the Python CV Backend
echo "Starting Python CV Backend on port 8000..."
cd python_backend
python3 run_cv_backend.py &
CV_PID=$!

# Wait a moment for the CV backend to start
sleep 3

# Start the Node.js frontend/backend
echo "Starting Node.js server on port 5000..."
cd ..
npm run dev &
NODE_PID=$!

# Function to cleanup on exit
cleanup() {
    echo "Shutting down servers..."
    kill $CV_PID $NODE_PID
    exit 0
}

# Trap cleanup function on script exit
trap cleanup EXIT

# Wait for either process to exit
wait $CV_PID $NODE_PID