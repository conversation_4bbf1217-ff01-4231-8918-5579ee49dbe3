import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronLeft, ChevronRight, RotateCw, Download, Share, Camera } from "lucide-react";
import type { Analysis } from "@shared/schema";

interface StreetViewPanelProps {
  analysis: Analysis;
}

export default function StreetViewPanel({ analysis }: StreetViewPanelProps) {
  const [currentViewIndex, setCurrentViewIndex] = useState(0);

  // Mock street view data - in real implementation this would come from the analysis
  const streetViews = [
    { direction: "Front", angle: "0°", label: "North View" },
    { direction: "Right", angle: "90°", label: "East View" },
    { direction: "Back", angle: "180°", label: "South View" },
    { direction: "Left", angle: "270°", label: "West View" }
  ];

  const mockStreetViewUrls = analysis.streetViewUrls || [];

  const nextView = () => {
    setCurrentViewIndex((prev) => (prev + 1) % streetViews.length);
  };

  const prevView = () => {
    setCurrentViewIndex((prev) => (prev - 1 + streetViews.length) % streetViews.length);
  };

  const currentView = streetViews[currentViewIndex];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold text-gray-800">Street View Analysis</h2>
        <p className="text-lg text-gray-600">360° property perspective for comprehensive solar assessment</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Street View Display */}
        <div className="lg:col-span-3">
          <Card className="shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-6 w-6 text-blue-600" />
                  {currentView.label} - {currentView.direction} Side
                </CardTitle>
                <div className="flex gap-2">
                  <Badge variant="outline" className="text-sm">
                    {currentView.angle}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <RotateCw className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Share className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="relative aspect-video bg-gray-200 rounded-b-lg overflow-hidden">
                {mockStreetViewUrls.length > currentViewIndex ? (
                  <img 
                    src={mockStreetViewUrls[currentViewIndex]}
                    alt={`Street view from ${currentView.direction.toLowerCase()} side`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <Camera className="h-12 w-12 text-gray-400 mx-auto" />
                      <p className="text-gray-500">Street view from {currentView.direction.toLowerCase()} side</p>
                      <p className="text-sm text-gray-400">Loading street view imagery...</p>
                    </div>
                  </div>
                )}

                {/* Navigation Controls */}
                <div className="absolute inset-y-0 left-0 flex items-center">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={prevView}
                    className="ml-4 bg-white/90 hover:bg-white shadow-lg"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </div>
                <div className="absolute inset-y-0 right-0 flex items-center">
                  <Button
                    variant="secondary"
                    size="sm"
                    onClick={nextView}
                    className="mr-4 bg-white/90 hover:bg-white shadow-lg"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>

                {/* View Indicator */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                  <div className="flex space-x-2">
                    {streetViews.map((view, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentViewIndex(index)}
                        className={`w-3 h-3 rounded-full transition-all ${
                          index === currentViewIndex 
                            ? 'bg-blue-600 scale-110' 
                            : 'bg-white/60 hover:bg-white/80'
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* View Analysis */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3 text-gray-800">{currentView.label} Analysis</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">Visible Roof Area:</span>
                  <p className="text-gray-800">Partial south-facing section visible</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Potential Shading:</span>
                  <p className="text-gray-800">Trees on {currentView.direction.toLowerCase()} side</p>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Access Points:</span>
                  <p className="text-gray-800">Clear installation path available</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Side Panel */}
        <div className="space-y-4">
          {/* View Selector */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">View Selection</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {streetViews.map((view, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentViewIndex(index)}
                  className={`w-full p-3 rounded-lg border text-left transition-all ${
                    index === currentViewIndex
                      ? 'border-blue-300 bg-blue-50 shadow-sm'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-800">{view.label}</p>
                      <p className="text-sm text-gray-500">{view.direction} Side • {view.angle}</p>
                    </div>
                    {index === currentViewIndex && (
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                    )}
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
            <CardContent className="p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Property Overview</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Building Type:</span>
                  <span className="font-medium">Residential</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Stories:</span>
                  <span className="font-medium">2 levels</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Roof Style:</span>
                  <span className="font-medium">Complex gabled</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Surroundings:</span>
                  <span className="font-medium">Suburban</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shading Analysis */}
          <Card className="bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200">
            <CardContent className="p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Shading Assessment</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Morning Shading:</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">Minimal</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Afternoon Shading:</span>
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Moderate</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Tree Coverage:</span>
                  <Badge variant="secondary" className="bg-orange-100 text-orange-800">Partial</Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Installation Notes */}
          <Card>
            <CardContent className="p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Installation Notes</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Clear access from driveway</li>
                <li>• Standard roof pitch for mounting</li>
                <li>• Electrical panel easily accessible</li>
                <li>• No major obstructions detected</li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}