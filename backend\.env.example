# RoofSnap Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# Application Settings
DEBUG=false
APP_NAME="RoofSnap API"
APP_VERSION="1.0.0"

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/roofsnap

# Google APIs (Required)
GOOGLE_API_KEY=your_google_maps_api_key_here

# Gemini AI (Required for AI summaries)
GEMINI_API_KEY=your_gemini_api_key_here

# CORS Settings (adjust for your frontend URL)
CORS_ORIGINS=["http://localhost:5173", "http://localhost:3000"]

# File Storage
DATA_DIRECTORY=../data

# Solar Panel Configuration
PANEL_WIDTH=1.6
PANEL_HEIGHT=1.0

# CV Backend URL (if using separate CV service)
CV_BACKEND_URL=http://localhost:8000
