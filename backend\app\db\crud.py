"""
CRUD operations for database models
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from typing import List, Optional, Dict, Any
import logging

from app.db.models import Analysis, User
from app.db.schemas import AnalysisCreate, AnalysisUpdate

logger = logging.getLogger(__name__)


class AnalysisCRUD:
    """CRUD operations for Analysis model"""
    
    @staticmethod
    async def create(db: AsyncSession, analysis_data: AnalysisCreate) -> Analysis:
        """Create new analysis record"""
        try:
            analysis = Analysis(**analysis_data.model_dump())
            db.add(analysis)
            await db.commit()
            await db.refresh(analysis)
            return analysis
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating analysis: {e}")
            raise
    
    @staticmethod
    async def get_by_id(db: AsyncSession, analysis_id: int) -> Optional[Analysis]:
        """Get analysis by ID"""
        try:
            result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting analysis {analysis_id}: {e}")
            raise
    
    @staticmethod
    async def get_all(
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Analysis]:
        """Get all analyses with optional filtering"""
        try:
            query = select(Analysis).order_by(Analysis.created_at.desc())
            
            if status:
                query = query.where(Analysis.status == status)
            
            query = query.offset(skip).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting analyses: {e}")
            raise
    
    @staticmethod
    async def update(
        db: AsyncSession, 
        analysis_id: int, 
        update_data: AnalysisUpdate
    ) -> Optional[Analysis]:
        """Update analysis record"""
        try:
            # Get existing record
            result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
            analysis = result.scalar_one_or_none()
            
            if not analysis:
                return None
            
            # Update fields
            update_dict = update_data.model_dump(exclude_unset=True)
            for field, value in update_dict.items():
                setattr(analysis, field, value)
            
            await db.commit()
            await db.refresh(analysis)
            return analysis
        except Exception as e:
            await db.rollback()
            logger.error(f"Error updating analysis {analysis_id}: {e}")
            raise
    
    @staticmethod
    async def update_status(
        db: AsyncSession, 
        analysis_id: int, 
        status: str
    ) -> Optional[Analysis]:
        """Update analysis status"""
        try:
            result = await db.execute(
                update(Analysis)
                .where(Analysis.id == analysis_id)
                .values(status=status)
                .returning(Analysis)
            )
            analysis = result.scalar_one_or_none()
            await db.commit()
            return analysis
        except Exception as e:
            await db.rollback()
            logger.error(f"Error updating status for analysis {analysis_id}: {e}")
            raise
    
    @staticmethod
    async def delete(db: AsyncSession, analysis_id: int) -> bool:
        """Delete analysis record"""
        try:
            result = await db.execute(
                delete(Analysis).where(Analysis.id == analysis_id)
            )
            await db.commit()
            return result.rowcount > 0
        except Exception as e:
            await db.rollback()
            logger.error(f"Error deleting analysis {analysis_id}: {e}")
            raise
    
    @staticmethod
    async def get_by_address(db: AsyncSession, address: str) -> List[Analysis]:
        """Get analyses by address"""
        try:
            result = await db.execute(
                select(Analysis)
                .where(Analysis.address.ilike(f"%{address}%"))
                .order_by(Analysis.created_at.desc())
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting analyses by address: {e}")
            raise
    
    @staticmethod
    async def get_completed_analyses(
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Analysis]:
        """Get completed analyses"""
        try:
            result = await db.execute(
                select(Analysis)
                .where(Analysis.status == "completed")
                .order_by(Analysis.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting completed analyses: {e}")
            raise
    
    @staticmethod
    async def get_statistics(db: AsyncSession) -> Dict[str, Any]:
        """Get analysis statistics"""
        try:
            # Count by status
            from sqlalchemy import func
            
            result = await db.execute(
                select(Analysis.status, func.count(Analysis.id))
                .group_by(Analysis.status)
            )
            status_counts = dict(result.all())
            
            # Total count
            total_result = await db.execute(select(func.count(Analysis.id)))
            total_count = total_result.scalar()
            
            # Average metrics for completed analyses
            avg_result = await db.execute(
                select(
                    func.avg(Analysis.panel_count),
                    func.avg(Analysis.annual_kwh),
                    func.avg(Analysis.roof_area)
                )
                .where(Analysis.status == "completed")
            )
            avg_panels, avg_kwh, avg_roof_area = avg_result.first()
            
            return {
                "total_analyses": total_count,
                "status_breakdown": status_counts,
                "averages": {
                    "panels": round(avg_panels or 0, 1),
                    "annual_kwh": round(avg_kwh or 0, 0),
                    "roof_area": round(avg_roof_area or 0, 1)
                }
            }
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            raise


class UserCRUD:
    """CRUD operations for User model (if needed for authentication)"""
    
    @staticmethod
    async def create(db: AsyncSession, username: str, password_hash: str) -> User:
        """Create new user"""
        try:
            user = User(username=username, password=password_hash)
            db.add(user)
            await db.commit()
            await db.refresh(user)
            return user
        except Exception as e:
            await db.rollback()
            logger.error(f"Error creating user: {e}")
            raise
    
    @staticmethod
    async def get_by_username(db: AsyncSession, username: str) -> Optional[User]:
        """Get user by username"""
        try:
            result = await db.execute(select(User).where(User.username == username))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user {username}: {e}")
            raise
    
    @staticmethod
    async def get_by_id(db: AsyncSession, user_id: int) -> Optional[User]:
        """Get user by ID"""
        try:
            result = await db.execute(select(User).where(User.id == user_id))
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting user {user_id}: {e}")
            raise
