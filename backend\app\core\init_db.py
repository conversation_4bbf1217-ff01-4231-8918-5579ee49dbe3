"""
Database initialization and setup utilities
"""
import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy import text

from app.core.config import get_settings
from app.db.database import Base, engine
from app.db.models import Analysis, User

logger = logging.getLogger(__name__)
settings = get_settings()


async def create_database_if_not_exists():
    """Create database if it doesn't exist"""
    try:
        # Extract database name from URL
        db_url_parts = settings.database_url.split('/')
        db_name = db_url_parts[-1]
        
        # Connect to postgres database to create our database
        postgres_url = '/'.join(db_url_parts[:-1]) + '/postgres'
        async_postgres_url = postgres_url.replace("postgresql://", "postgresql+asyncpg://")
        
        temp_engine = create_async_engine(async_postgres_url)
        
        async with temp_engine.begin() as conn:
            # Check if database exists
            result = await conn.execute(
                text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                {"db_name": db_name}
            )
            
            if not result.fetchone():
                # Create database
                await conn.execute(text("COMMIT"))  # End transaction
                await conn.execute(text(f"CREATE DATABASE {db_name}"))
                logger.info(f"Created database: {db_name}")
            else:
                logger.info(f"Database {db_name} already exists")
        
        await temp_engine.dispose()
        
    except Exception as e:
        logger.warning(f"Could not create database automatically: {e}")
        logger.info("Please ensure the database exists manually")


async def create_tables():
    """Create all database tables"""
    try:
        async with engine.begin() as conn:
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating tables: {e}")
        raise


async def drop_tables():
    """Drop all database tables (use with caution!)"""
    try:
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Error dropping tables: {e}")
        raise


async def reset_database():
    """Reset database by dropping and recreating all tables"""
    try:
        await drop_tables()
        await create_tables()
        logger.info("Database reset completed")
    except Exception as e:
        logger.error(f"Error resetting database: {e}")
        raise


async def seed_sample_data():
    """Insert sample data for testing"""
    from app.db.database import AsyncSessionLocal
    
    try:
        async with AsyncSessionLocal() as db:
            # Check if we already have data
            from sqlalchemy import select, func
            result = await db.execute(select(func.count(Analysis.id)))
            count = result.scalar()
            
            if count > 0:
                logger.info("Sample data already exists, skipping seed")
                return
            
            # Create sample analyses
            sample_analyses = [
                Analysis(
                    address="1600 Amphitheatre Parkway, Mountain View, CA",
                    latitude=37.4220,
                    longitude=-122.0841,
                    status="completed",
                    roof_area=2500.0,
                    usable_area=2000.0,
                    panel_count=25,
                    annual_kwh=12500,
                    efficiency=0.85,
                    grade="A",
                    ai_summary="Excellent solar potential with optimal roof orientation and minimal shading.",
                    recommendations=["Proceed with installation", "Consider high-efficiency panels", "Schedule professional assessment"]
                ),
                Analysis(
                    address="1 Hacker Way, Menlo Park, CA",
                    latitude=37.4845,
                    longitude=-122.1477,
                    status="completed",
                    roof_area=1800.0,
                    usable_area=1400.0,
                    panel_count=18,
                    annual_kwh=9200,
                    efficiency=0.78,
                    grade="B+",
                    ai_summary="Good solar potential with some shading considerations.",
                    recommendations=["Consider tree trimming", "Install optimizers", "Good investment opportunity"]
                ),
                Analysis(
                    address="1 Apple Park Way, Cupertino, CA",
                    latitude=37.3349,
                    longitude=-122.0090,
                    status="processing"
                )
            ]
            
            for analysis in sample_analyses:
                db.add(analysis)
            
            await db.commit()
            logger.info("Sample data seeded successfully")
            
    except Exception as e:
        logger.error(f"Error seeding sample data: {e}")
        raise


async def check_database_connection():
    """Check if database connection is working"""
    try:
        from app.db.database import AsyncSessionLocal
        
        async with AsyncSessionLocal() as db:
            # Simple query to test connection
            result = await db.execute(text("SELECT 1"))
            result.fetchone()
            logger.info("Database connection successful")
            return True
            
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False


async def get_database_info():
    """Get database information and statistics"""
    try:
        from app.db.database import AsyncSessionLocal
        from sqlalchemy import text
        
        async with AsyncSessionLocal() as db:
            # Get PostgreSQL version
            version_result = await db.execute(text("SELECT version()"))
            version = version_result.scalar()
            
            # Get table information
            tables_result = await db.execute(text("""
                SELECT table_name, 
                       pg_size_pretty(pg_total_relation_size(quote_ident(table_name))) as size
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            tables = tables_result.fetchall()
            
            # Get analysis count
            from sqlalchemy import select, func
            count_result = await db.execute(select(func.count(Analysis.id)))
            analysis_count = count_result.scalar()
            
            return {
                "version": version,
                "tables": [{"name": t[0], "size": t[1]} for t in tables],
                "analysis_count": analysis_count,
                "connection_status": "healthy"
            }
            
    except Exception as e:
        logger.error(f"Error getting database info: {e}")
        return {
            "connection_status": "error",
            "error": str(e)
        }


async def init_database():
    """Initialize database with all necessary setup"""
    try:
        logger.info("Initializing database...")
        
        # Check connection first
        if not await check_database_connection():
            # Try to create database if connection fails
            await create_database_if_not_exists()
        
        # Create tables
        await create_tables()
        
        # Optionally seed sample data in development
        if settings.debug:
            await seed_sample_data()
        
        logger.info("Database initialization completed successfully")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


if __name__ == "__main__":
    # Run database initialization
    asyncio.run(init_database())
