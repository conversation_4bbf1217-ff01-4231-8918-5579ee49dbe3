# RoofSnap - AI-Powered Solar Analysis Platform

## 🌟 Overview

RoofSnap is a production-ready, full-stack web application that analyzes roof suitability and solar panel potential using advanced computer vision and AI. The platform provides comprehensive solar assessments with professional reporting for residential and commercial properties.

## 🏗️ Refactored Architecture

This project has been completely refactored from a mixed Node.js/Python stack to a clean, maintainable architecture:

### **Frontend** (`/client/`)
- **React + TypeScript** with modern hooks and components
- **Tailwind CSS** with shadcn/ui component library
- **TanStack Query** for efficient server state management
- **Vite** for fast development and optimized builds
- **Responsive Design** with mobile-first approach

### **Backend** (`/backend/`)
- **FastAPI** with async/await for high performance
- **PostgreSQL** with SQLAlchemy ORM for robust data management
- **Computer Vision** using OpenCV for roof detection
- **Google APIs** integration (Maps, Geocoding, Street View)
- **Gemini AI** for intelligent summaries and recommendations
- **Professional Reports** in PDF and CSV formats

## 📁 Project Structure

```
RoofSnap/
├── client/                           # React frontend
│   └── src/
│       ├── components/               # Reusable UI components
│       │   └── MapOverlayViewer.tsx
│       ├── pages/                    # Page components
│       │   └── Home.tsx
│       ├── lib/                      # Utilities (e.g., API fetchers)
│       │   └── queryClient.ts
│       ├── App.tsx
│       ├── main.tsx
│       └── index.css
│
├── backend/                          # Python + FastAPI backend
│   ├── app/
│   │   ├── api/                      # API endpoints
│   │   │   ├── routes.py
│   │   │   └── roof_analysis.py
│   │   ├── core/                     # Core config, startup, env
│   │   │   ├── config.py
│   │   │   └── init_db.py
│   │   ├── db/                       # Database models & logic
│   │   │   ├── database.py
│   │   │   ├── models.py
│   │   │   ├── schemas.py
│   │   │   └── crud.py
│   │   ├── services/                 # Business logic: CV, AI, elevation
│   │   │   ├── vision.py
│   │   │   ├── solar_math.py
│   │   │   ├── gemini_summary.py
│   │   │   ├── geocoding.py
│   │   │   ├── imagery.py
│   │   │   ├── ai_analysis.py
│   │   │   └── report_generator.py
│   │   └── main.py                   # FastAPI entrypoint
│   ├── .env                          # API keys, DB URL
│   ├── requirements.txt              # Python dependencies
│   └── README.md                     # Backend setup guide
│
├── data/                             # Local image and CSV outputs
│   ├── satellite/
│   ├── streetview/
│   ├── overlays/
│   └── reports/
│
├── shared/                           # Shared schema/types if needed
│   └── schema.ts
│
├── test_system.py                    # Comprehensive system test
├── start_backend.bat                 # Windows backend startup script
├── .gitignore
└── README.md                         # This file
```

## 🚀 Quick Start

### Prerequisites

1. **Python 3.9+** - [Download here](https://python.org)
2. **Node.js 18+** - [Download here](https://nodejs.org)
3. **PostgreSQL 13+** - [Download here](https://postgresql.org)

### Backend Setup

1. **Navigate to backend directory**:
   ```powershell
   cd backend
   ```

2. **Create virtual environment**:
   ```powershell
   python -m venv venv
   venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```powershell
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```powershell
   copy .env.example .env
   # Edit .env with your API keys and database URL
   ```

5. **Start backend**:
   ```powershell
   uvicorn app.main:app --reload --port 8000
   ```

### Frontend Setup

1. **Navigate to client directory**:
   ```powershell
   cd client
   ```

2. **Install dependencies**:
   ```powershell
   npm install
   ```

3. **Start development server**:
   ```powershell
   npm run dev
   ```

### Access the Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔑 Required API Keys

### Google Maps API
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Enable these APIs:
   - Maps Static API
   - Street View Static API
   - Geocoding API
   - Elevation API
3. Create API key and add to `.env`

### Gemini AI API
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create API key and add to `.env`

## 🧪 Testing

Run the comprehensive system test:

```powershell
python test_system.py
```

This will test all major functionality including:
- API connectivity
- Geocoding services
- Roof analysis
- Report generation
- Database operations

## 🌟 Key Features

### Computer Vision Analysis
- **Roof Detection**: Advanced edge detection and contour analysis
- **Solar Panel Placement**: Optimal grid generation with efficiency scoring
- **Shading Analysis**: Impact assessment of obstructions
- **Real-world Measurements**: Accurate pixel-to-meter conversion

### AI-Powered Insights
- **Executive Summaries**: Business-focused analysis for decision makers
- **Technical Reports**: Detailed specifications for installers
- **Financial Projections**: ROI analysis and payback calculations
- **Smart Recommendations**: Actionable optimization suggestions

### Professional Reporting
- **PDF Reports**: Comprehensive analysis with visualizations
- **CSV Data Export**: Detailed panel-by-panel specifications
- **Interactive Visualizations**: Color-coded efficiency maps
- **Financial Modeling**: 25-year production and savings projections

## 🔧 API Endpoints

### Core Analysis
- `POST /api/analyze` - Start comprehensive analysis
- `GET /api/analyses/{id}` - Get analysis results
- `POST /api/geocode` - Convert address to coordinates

### Specialized Roof Analysis
- `POST /api/roof/analyze-image` - Analyze uploaded image
- `POST /api/roof/analyze-coordinates` - Analyze from coordinates
- `GET /api/roof/detection-methods` - Available CV methods

### Reports
- `GET /api/analyses/{id}/report/pdf` - Download PDF report
- `GET /api/analyses/{id}/report/csv` - Download CSV data

## 🛠️ Development

### Backend Development
- FastAPI with automatic OpenAPI documentation
- Async/await for high performance
- SQLAlchemy ORM with PostgreSQL
- Comprehensive error handling and logging

### Frontend Development
- React with TypeScript for type safety
- TanStack Query for server state management
- Tailwind CSS for responsive design
- Component-based architecture

## 📊 Performance

- **Analysis Speed**: ~30-60 seconds per property
- **Accuracy**: 85%+ roof detection confidence
- **Scalability**: Async processing for concurrent analyses
- **Reliability**: Comprehensive error handling and fallbacks

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check `/backend/README.md` for detailed setup
- **Issues**: Report bugs via GitHub Issues
- **Testing**: Run `python test_system.py` for diagnostics

---

**Built with ❤️ for the solar energy revolution** 🌞
