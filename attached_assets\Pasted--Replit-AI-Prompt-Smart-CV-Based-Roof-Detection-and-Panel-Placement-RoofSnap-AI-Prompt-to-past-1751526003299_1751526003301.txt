✅ Replit AI Prompt: "Smart CV-Based Roof Detection and Panel Placement (RoofSnap AI)"
Prompt to paste into Replit AI:

I’m building a production-ready full-stack app called RoofSnap, which helps solar companies assess roof suitability and generate aligned solar panel layouts using AI and computer vision.

I want you to help me create the backend pipeline and integrate it with the frontend, using my existing Replit full-stack setup (React frontend + Python FastAPI backend).
Avoid over-reliance on Google Solar API — instead, use AI/CV logic for real insights.

🎯 Backend Objective:
Build a Python-based CV pipeline that performs intelligent solar panel layout on rooftops using satellite and street-view images. Here's the full logic I want you to implement:

📸 Step 1: Satellite Image Input
Accept a user-entered address and fetch satellite image using Google Static Maps API.

Optionally also use Street View images for elevation.

🧠 Step 2: Roof Detection (Computer Vision)
Use YOLOv8 (pre-trained or fine-tuned) or Segment Anything Model (SAM) to detect the roof surface in the satellite image.

Output should be:

A binary mask or polygon of the roof

A cleaned contour (polygon) of usable roof zone

🔺 Step 3: Elevation and Tilt Estimation
Use Google Elevation API or infer from Street View images (if possible).

Estimate:

Roof pitch (in degrees)

Orientation (azimuth)

Any large obstructions (chimney, trees, HVAC, etc.)

🔳 Step 4: Grid and Panel Tiling Logic
Generate a grid of panel-sized tiles (~1.6m x 1m per panel) inside the detected roof polygon

Use geometric logic (Shapely or OpenCV) to:

Align panels with the roof orientation

Clip the grid to roof boundary

Avoid overlapping shaded areas or obstacles

🌗 Step 5: Shading Detection (Optional)
Use brightness levels or trained CV model to detect shade from trees or nearby structures

Mark those tiles as low solar yield

🔥 Step 6: Panel Heatmap Generation
Color each tile based on basic solar estimation:

Angle of roof

Orientation (N/E/S/W)

Shading score

Use a heatmap scale like:

🔴 Red: Low potential

🟠 Orange: Medium-low

🟡 Yellow: Medium

🟢 Green: High energy potential

📊 Step 7: Solar Summary Calculation
From the valid panel area:

Count total usable panels

Estimate total annual kWh (use avg solar irradiance dataset per location)

Compute CO₂ savings, area used, % efficiency

Send this structured data to frontend + pass to LLM (Gemini) for summary text

🖼️ Step 8: Frontend Integration (React)
Output the final panel grid + heatmap overlay as:

SVG or Canvas on top of base satellite image

Aligned exactly to the roof (rotated if required)

Add toggle for hiding/showing overlay

Include panel statistics and Gemini summary

Add export button for report (PDF/CSV)

📦 Tech to Use:
YOLOv8 or SAM for roof detection

OpenCV and Shapely for grid + clipping

Google Static Maps & Elevation API

NumPy for solar physics calculations

FastAPI backend

React + Tailwind frontend

📍 Constraints:
This is for solar B2B users, so accuracy and visual clarity are essential.

Avoid using mock data. Use real CV/image analysis.

Output should look similar to:

Google Project Sunroof

Tesla solar roof estimator

ArcGIS rooftop solar plugin

Can you implement this pipeline starting with the roof detection and grid panel alignment logic, and expose it via a FastAPI endpoint that returns:

The overlaid panel map (as image or SVG)

A JSON with solar stats and analysis summary

Let me know if you need sample images or real addresses — I’ll provide.

✅ This prompt will guide Replit AI to:

Build actual CV + geometric logic

Create a reusable API

Integrate cleanly with your React UI

Work without total dependency on Google Solar API