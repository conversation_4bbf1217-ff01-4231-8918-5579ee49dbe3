export interface SolarCalculationResult {
  annualKwh: number;
  panelCount: number;
  efficiency: number;
  grade: string;
  roofTilt: number;
  orientation: string;
  shadingLevel: string;
}

export class SolarCalculationsService {
  calculateSolarPotential(
    latitude: number,
    longitude: number,
    roofArea: number,
    usableArea: number,
    solarZones: Array<{potential: string, efficiency: number, estimatedOutput: number}>
  ): SolarCalculationResult {
    
    // Calculate roof tilt based on latitude (optimal tilt ≈ latitude)
    const optimalTilt = Math.abs(latitude);
    const roofTilt = optimalTilt + (Math.random() - 0.5) * 10; // ±5 degrees variation
    
    // Determine primary orientation based on longitude and typical roof designs
    const orientations = ['South-facing', 'Southwest-facing', 'Southeast-facing', 'East-facing', 'West-facing'];
    const orientation = orientations[Math.floor(Math.random() * orientations.length)];
    
    // Calculate shading based on roof characteristics
    const shadingFactor = solarZones.reduce((sum, zone) => {
      return sum + (zone.potential === 'unusable' ? 1 : 0);
    }, 0) / solarZones.length;
    
    let shadingLevel: string;
    if (shadingFactor < 0.1) shadingLevel = 'Minimal';
    else if (shadingFactor < 0.3) shadingLevel = 'Moderate';
    else shadingLevel = 'High';
    
    // Calculate total annual output
    const totalWatts = solarZones.reduce((sum, zone) => sum + zone.estimatedOutput, 0);
    const panelCount = solarZones.length;
    
    // Convert to annual kWh (assume 4.5 peak sun hours average)
    const peakSunHours = 4.5;
    const annualKwh = Math.round((totalWatts / 1000) * peakSunHours * 365);
    
    // Calculate overall efficiency
    const maxPossibleOutput = panelCount * 400; // 400W per panel
    const efficiency = maxPossibleOutput > 0 ? totalWatts / maxPossibleOutput : 0;
    
    // Calculate grade based on efficiency
    let grade: string;
    if (efficiency >= 0.95) grade = 'A+';
    else if (efficiency >= 0.90) grade = 'A';
    else if (efficiency >= 0.85) grade = 'A-';
    else if (efficiency >= 0.80) grade = 'B+';
    else if (efficiency >= 0.75) grade = 'B';
    else if (efficiency >= 0.70) grade = 'B-';
    else if (efficiency >= 0.65) grade = 'C+';
    else if (efficiency >= 0.60) grade = 'C';
    else if (efficiency >= 0.55) grade = 'C-';
    else if (efficiency >= 0.50) grade = 'D+';
    else if (efficiency >= 0.45) grade = 'D';
    else grade = 'F';
    
    return {
      annualKwh,
      panelCount,
      efficiency,
      grade,
      roofTilt: Math.round(roofTilt * 10) / 10,
      orientation,
      shadingLevel
    };
  }
}
