import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export class DataStorageMiddleware {
  private static instance: DataStorageMiddleware;
  private dataDir: string;

  private constructor() {
    this.dataDir = path.join(__dirname, '../../data');
    this.ensureDirectoryExists();
  }

  static getInstance(): DataStorageMiddleware {
    if (!DataStorageMiddleware.instance) {
      DataStorageMiddleware.instance = new DataStorageMiddleware();
    }
    return DataStorageMiddleware.instance;
  }

  private ensureDirectoryExists(): void {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  getDataDir(): string {
    return this.dataDir;
  }

  saveFile(filename: string, data: Buffer | string): string {
    const filepath = path.join(this.dataDir, filename);
    fs.writeFileSync(filepath, data);
    return filepath;
  }

  getFile(filename: string): Buffer {
    const filepath = path.join(this.dataDir, filename);
    return fs.readFileSync(filepath);
  }

  fileExists(filename: string): boolean {
    const filepath = path.join(this.dataDir, filename);
    return fs.existsSync(filepath);
  }

  deleteFile(filename: string): boolean {
    try {
      const filepath = path.join(this.dataDir, filename);
      fs.unlinkSync(filepath);
      return true;
    } catch {
      return false;
    }
  }

  listFiles(): string[] {
    return fs.readdirSync(this.dataDir);
  }

  cleanupOldFiles(maxAgeHours: number = 24): void {
    const files = fs.readdirSync(this.dataDir);
    const now = Date.now();
    const maxAgeMs = maxAgeHours * 60 * 60 * 1000;

    files.forEach(file => {
      const filepath = path.join(this.dataDir, file);
      const stats = fs.statSync(filepath);
      
      if (now - stats.mtime.getTime() > maxAgeMs) {
        fs.unlinkSync(filepath);
      }
    });
  }
}
