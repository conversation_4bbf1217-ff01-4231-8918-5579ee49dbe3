# RoofSnap FastAPI Backend

A clean Python + FastAPI backend for RoofSnap solar analysis platform.

## Features

- **FastAPI** web framework with async support
- **PostgreSQL** database with SQLAlchemy ORM
- **Computer Vision** roof detection and solar panel placement
- **Google APIs** integration for geocoding and imagery
- **Gemini AI** integration for intelligent summaries
- **PDF/CSV** report generation
- **RESTful API** with automatic documentation

## Setup Instructions (Windows)

### Prerequisites

1. **Python 3.9+** - Download from [python.org](https://python.org)
2. **PostgreSQL** - Download from [postgresql.org](https://postgresql.org)
3. **Git** - Download from [git-scm.com](https://git-scm.com)

### Installation

1. **Clone the repository** (if not already done):
   ```powershell
   git clone <repository-url>
   cd RoofSnap
   ```

2. **Create Python virtual environment**:
   ```powershell
   cd backend
   python -m venv venv
   venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```powershell
   pip install -r requirements.txt
   ```

4. **Set up environment variables**:
   ```powershell
   copy .env.example .env
   ```
   
   Edit `.env` file with your actual values:
   ```env
   GOOGLE_API_KEY=your_google_maps_api_key_here
   GEMINI_API_KEY=your_gemini_api_key_here
   DATABASE_URL=postgresql://postgres:password@localhost:5432/roofsnap
   ```

5. **Set up PostgreSQL database**:
   ```sql
   -- Connect to PostgreSQL as superuser
   CREATE DATABASE roofsnap;
   CREATE USER roofsnap_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE roofsnap TO roofsnap_user;
   ```

6. **Run database migrations** (tables will be created automatically on first run):
   ```powershell
   # The app will create tables automatically when started
   ```

### Running the Backend

1. **Activate virtual environment** (if not already active):
   ```powershell
   venv\Scripts\activate
   ```

2. **Start the FastAPI server**:
   ```powershell
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

3. **Access the API**:
   - API Base URL: `http://localhost:8000`
   - Interactive Docs: `http://localhost:8000/docs`
   - ReDoc: `http://localhost:8000/redoc`

### API Endpoints

- `GET /` - Root endpoint
- `GET /health` - Health check
- `POST /api/geocode` - Geocode address
- `POST /api/analyze` - Start roof analysis
- `GET /api/analyses` - List all analyses
- `GET /api/analyses/{id}` - Get specific analysis
- `GET /api/analyses/{id}/report/pdf` - Download PDF report
- `GET /api/analyses/{id}/report/csv` - Download CSV report

### Required API Keys

1. **Google Maps API Key**:
   - Go to [Google Cloud Console](https://console.cloud.google.com)
   - Enable: Maps Static API, Street View Static API, Geocoding API, Elevation API
   - Create API key and add to `.env`

2. **Gemini API Key**:
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create API key and add to `.env`

### Development

- **Auto-reload**: The server automatically reloads when code changes
- **Logging**: Check console for detailed logs
- **Database**: Tables are created automatically on startup
- **Static Files**: Images and reports are served from `/data` endpoint

### Troubleshooting

1. **Import Errors**: Make sure virtual environment is activated
2. **Database Errors**: Check PostgreSQL is running and credentials are correct
3. **API Key Errors**: Verify API keys are valid and have required permissions
4. **Port Conflicts**: Change port in uvicorn command if 8000 is in use

### Project Structure

```
backend/
├── app/
│   ├── api/
│   │   └── routes.py          # API endpoints
│   ├── core/
│   │   └── config.py          # Configuration settings
│   ├── db/
│   │   ├── database.py        # Database connection
│   │   ├── models.py          # SQLAlchemy models
│   │   └── schemas.py         # Pydantic schemas
│   ├── services/
│   │   ├── ai_analysis.py     # AI analysis orchestration
│   │   ├── geocoding.py       # Google Geocoding API
│   │   ├── imagery.py         # Google Maps/Street View API
│   │   ├── vision.py          # Computer vision services
│   │   └── report_generator.py # PDF/CSV generation
│   └── main.py                # FastAPI application
├── requirements.txt           # Python dependencies
├── .env.example              # Environment template
└── README.md                 # This file
```
