"""
AI Analysis service that orchestrates roof detection, solar calculations, and AI summaries
"""
import aiohttp
import logging
from typing import Dict, Any, List, Optional
from fastapi import HTTPException

from app.core.config import get_settings
from app.services.vision import VisionService
from app.services.imagery import ImageryService
from app.services.geocoding import GeocodingService

logger = logging.getLogger(__name__)
settings = get_settings()


class GeminiService:
    """Service for generating AI summaries using Google Gemini API"""
    
    def __init__(self):
        self.api_key = settings.gemini_api_key
        if not self.api_key:
            logger.warning("Gemini API key not configured - AI summaries will be disabled")
    
    async def generate_summary(
        self,
        analysis_data: Dict[str, Any],
        address: str
    ) -> str:
        """
        Generate AI summary of solar analysis using Gemini
        
        Args:
            analysis_data: Complete analysis results
            address: Property address
            
        Returns:
            AI-generated summary text
        """
        if not self.api_key:
            return "AI summary unavailable - Gemini API key not configured"
        
        # Prepare prompt for Gemini
        prompt = self._create_analysis_prompt(analysis_data, address)
        
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.api_key}"
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }]
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status != 200:
                        logger.error(f"Gemini API error: {response.status}")
                        return "AI summary temporarily unavailable"
                    
                    data = await response.json()
                    
                    if not data.get("candidates"):
                        logger.error("No content generated by Gemini")
                        return "AI summary could not be generated"
                    
                    generated_text = data["candidates"][0]["content"]["parts"][0]["text"]
                    return generated_text.strip()
                    
        except Exception as e:
            logger.error(f"Error generating Gemini summary: {e}")
            return "AI summary temporarily unavailable"
    
    def _create_analysis_prompt(self, analysis_data: Dict[str, Any], address: str) -> str:
        """Create prompt for Gemini analysis"""
        solar_analysis = analysis_data.get("solar_analysis", {})
        roof_detection = analysis_data.get("roof_detection", {})
        solar_grid = analysis_data.get("solar_grid", {})
        
        prompt = f"""
        Please provide a professional solar analysis summary for the property at {address}.
        
        Analysis Results:
        - Total Solar Panels: {solar_analysis.get('total_panels', 0)}
        - Annual Energy Production: {solar_analysis.get('annual_kwh', 0):,.0f} kWh
        - Total System Output: {solar_analysis.get('total_output', 0):,.0f} watts
        - Overall Efficiency: {solar_analysis.get('efficiency', 0):.1%}
        - Solar Grade: {solar_analysis.get('grade', 'N/A')}
        - Roof Area: {solar_grid.get('roof_area_m2', 0):.1f} square meters
        - Panel Coverage: {solar_grid.get('panel_coverage', 0):.1f} square meters
        
        Panel Breakdown:
        - High Potential Panels: {solar_analysis.get('panel_breakdown', {}).get('high', 0)}
        - Medium Potential Panels: {solar_analysis.get('panel_breakdown', {}).get('medium', 0)}
        - Low Potential Panels: {solar_analysis.get('panel_breakdown', {}).get('low', 0)}
        
        Please provide:
        1. A brief executive summary (2-3 sentences)
        2. Key findings about solar potential
        3. Estimated financial benefits (assume $0.12/kWh electricity rate)
        4. Recommendations for optimization
        5. Any limitations or considerations
        
        Keep the response professional, concise, and actionable for a homeowner considering solar installation.
        """
        
        return prompt


class AIAnalysisService:
    """Main AI analysis service that orchestrates the complete analysis pipeline"""
    
    def __init__(self):
        self.vision_service = VisionService()
        self.imagery_service = ImageryService()
        self.geocoding_service = GeocodingService()
        self.gemini_service = GeminiService()
    
    async def perform_complete_analysis(
        self,
        address: str
    ) -> Dict[str, Any]:
        """
        Perform complete analysis pipeline from address to final results
        
        Args:
            address: Property address to analyze
            
        Returns:
            Complete analysis results including AI summary
        """
        try:
            # Step 1: Geocode address
            logger.info(f"Geocoding address: {address}")
            geocode_result = await self.geocoding_service.geocode_address(address)
            
            latitude = geocode_result["latitude"]
            longitude = geocode_result["longitude"]
            formatted_address = geocode_result["formattedAddress"]
            
            # Step 2: Fetch satellite imagery
            logger.info(f"Fetching imagery for coordinates: {latitude}, {longitude}")
            imagery_result = await self.imagery_service.fetch_all_images(latitude, longitude)
            
            # Step 3: Perform computer vision analysis
            logger.info("Performing roof analysis")
            
            # Get satellite image data for CV analysis
            satellite_path = imagery_result["local_paths"]["satellite"]
            with open(satellite_path, 'rb') as f:
                image_data = f.read()
            
            cv_result = await self.vision_service.analyze_roof_from_image(
                image_data, latitude, longitude
            )
            
            if not cv_result.get("success"):
                raise HTTPException(
                    status_code=500,
                    detail=f"Computer vision analysis failed: {cv_result.get('error')}"
                )
            
            # Step 4: Generate AI summary
            logger.info("Generating AI summary")
            ai_summary = await self.gemini_service.generate_summary(cv_result, formatted_address)
            
            # Step 5: Generate recommendations
            recommendations = self._generate_recommendations(cv_result)
            
            # Compile complete results
            complete_result = {
                "address": formatted_address,
                "latitude": latitude,
                "longitude": longitude,
                "satellite_image_url": imagery_result["satellite_image_url"],
                "street_view_urls": imagery_result["street_view_urls"],
                "roof_area": cv_result["solar_grid"]["roof_area_m2"] * 10.764,  # Convert to sq ft
                "usable_area": cv_result["solar_grid"]["panel_coverage"] * 10.764,  # Convert to sq ft
                "panel_count": cv_result["solar_analysis"]["total_panels"],
                "annual_kwh": cv_result["solar_analysis"]["annual_kwh"],
                "efficiency": cv_result["solar_analysis"]["efficiency"],
                "grade": cv_result["solar_analysis"]["grade"],
                "solar_grid": {
                    "zones": cv_result["solar_grid"]["panels"]
                },
                "ai_summary": ai_summary,
                "recommendations": recommendations,
                "visualization_url": cv_result.get("visualization", ""),
                "roof_tilt": None,  # Could be enhanced with additional analysis
                "orientation": "South-facing",  # Default assumption
                "shading_level": "Minimal"  # Default assumption
            }
            
            return complete_result
            
        except Exception as e:
            logger.error(f"Complete analysis failed: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Analysis failed: {str(e)}"
            )
    
    def _generate_recommendations(self, cv_result: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on analysis results"""
        recommendations = []
        
        solar_analysis = cv_result.get("solar_analysis", {})
        grade = solar_analysis.get("grade", "F")
        total_panels = solar_analysis.get("total_panels", 0)
        panel_breakdown = solar_analysis.get("panel_breakdown", {})
        
        if grade in ["A", "B"]:
            recommendations.append("Excellent solar potential - proceed with installation planning")
        elif grade == "C":
            recommendations.append("Good solar potential with some optimization opportunities")
        else:
            recommendations.append("Limited solar potential - consider roof modifications or alternative solutions")
        
        if total_panels > 20:
            recommendations.append("Large roof area allows for significant energy production")
        elif total_panels < 10:
            recommendations.append("Limited roof space - consider high-efficiency panels")
        
        high_potential = panel_breakdown.get("high", 0)
        total = sum(panel_breakdown.values())
        
        if total > 0 and high_potential / total < 0.5:
            recommendations.append("Consider removing obstructions or trimming vegetation to improve panel efficiency")
        
        recommendations.append("Consult with local solar installers for detailed system design")
        recommendations.append("Check local incentives and net metering policies")
        
        return recommendations
