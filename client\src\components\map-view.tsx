import { <PERSON>mIn, ZoomOut, Maximize2, FileText, Download as DownloadIcon, Share } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { Analysis } from "@shared/schema";

interface MapViewProps {
  analysis: Analysis;
}

export default function MapView({ analysis }: MapViewProps) {
  const renderSolarGrid = () => {
    if (!analysis.solarGrid?.zones || analysis.solarGrid.zones.length === 0) return null;

    return (
      <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 1 1" preserveAspectRatio="none">
        {analysis.solarGrid.zones.map((zone, index) => {
          const x = zone.x;
          const y = zone.y;
          const width = zone.width;
          const height = zone.height;
          
          let color = '';
          switch (zone.potential) {
            case 'high':
              color = '#22c55e'; // Green
              break;
            case 'medium':
              color = '#eab308'; // Yellow
              break;
            case 'low':
              color = '#f97316'; // Orange
              break;
            case 'unusable':
              color = '#ef4444'; // Red
              break;
            default:
              color = '#6b7280'; // Gray
          }

          return (
            <rect
              key={index}
              x={x}
              y={y}
              width={width}
              height={height}
              fill={color}
              fillOpacity="0.7"
              stroke={color}
              strokeWidth="0.001"
            />
          );
        })}
      </svg>
    );
  };

  const legendItems = [
    { color: 'bg-accent', label: 'High (>85% efficiency)', count: analysis.solarGrid?.zones?.filter(z => z.potential === 'high').length || 0 },
    { color: 'bg-warning', label: 'Medium (60-85% efficiency)', count: analysis.solarGrid?.zones?.filter(z => z.potential === 'medium').length || 0 },
    { color: 'bg-destructive', label: 'Low (<60% efficiency)', count: analysis.solarGrid?.zones?.filter(z => z.potential === 'low').length || 0 },
    { color: 'bg-gray-300', label: 'Unusable (shaded/obstructed)', count: analysis.solarGrid?.zones?.filter(z => z.potential === 'unusable').length || 0 },
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main Map Display */}
      <div className="lg:col-span-2">
        <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden relative">
          {analysis.satelliteImageUrl ? (
            <img 
              src={analysis.satelliteImageUrl}
              alt="Satellite view of property"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
              <p className="text-gray-500">Loading satellite image...</p>
            </div>
          )}
          
          {/* Solar Panel Grid Overlay */}
          {renderSolarGrid()}
          
          {/* Map Controls */}
          <div className="absolute top-4 right-4 bg-white rounded-lg shadow-md p-2 space-y-2">
            <Button variant="outline" size="sm" className="w-8 h-8 p-0">
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" className="w-8 h-8 p-0">
              <ZoomOut className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" className="w-8 h-8 p-0">
              <Maximize2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Map Legend */}
        <div className="mt-4 bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-3">Solar Potential Legend</h4>
          <div className="grid grid-cols-2 gap-4">
            {legendItems.map((item, index) => (
              <div key={index} className="flex items-center">
                <div className={`w-4 h-4 ${item.color} rounded mr-2`}></div>
                <span className="text-sm">{item.label} ({item.count})</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Analysis Panel */}
      <div className="space-y-6">
        {/* Quick Stats */}
        <Card className="bg-gradient-to-r from-primary to-blue-600 text-white">
          <CardHeader>
            <CardTitle className="text-white">Analysis Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-blue-100">Usable Roof Area:</span>
              <span className="font-semibold">{analysis.usableArea?.toLocaleString() || 0} sq ft</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-100">Solar Panels:</span>
              <span className="font-semibold">{analysis.panelCount || 0} panels</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-100">Annual kWh:</span>
              <span className="font-semibold">{analysis.annualKwh?.toLocaleString() || 0} kWh</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-100">Efficiency Grade:</span>
              <span className="font-semibold">{analysis.grade || 'N/A'}</span>
            </div>
          </CardContent>
        </Card>
        
        {/* Property Details */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Property Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Address:</span>
              <span className="text-right">{analysis.address}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Roof Tilt:</span>
              <span>{analysis.roofTilt || 'N/A'}°</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Orientation:</span>
              <span>{analysis.orientation || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Shading:</span>
              <span>{analysis.shadingLevel || 'N/A'}</span>
            </div>
          </CardContent>
        </Card>
        
        {/* Action Buttons */}
        <div className="space-y-3">
          <Button className="w-full bg-accent hover:bg-accent/90">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
          <Button variant="outline" className="w-full">
            <DownloadIcon className="h-4 w-4 mr-2" />
            Export Data
          </Button>
          <Button variant="outline" className="w-full">
            <Share className="h-4 w-4 mr-2" />
            Share Analysis
          </Button>
        </div>
      </div>
    </div>
  );
}
