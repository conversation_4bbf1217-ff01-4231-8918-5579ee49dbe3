import { FileText, Download as DownloadIcon, Share2, Mail } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import type { Analysis } from "@shared/schema";

interface DownloadProps {
  analysis: Analysis;
}

export default function DownloadSection({ analysis }: DownloadProps) {
  const { toast } = useToast();

  const handleDownloadPDF = () => {
    if (analysis.pdfReportPath) {
      const url = `/api/analyses/${analysis.id}/report/pdf`;
      window.open(url, '_blank');
    } else {
      toast({
        title: "Report Not Ready",
        description: "PDF report is still being generated. Please try again in a moment.",
        variant: "destructive",
      });
    }
  };

  const handleDownloadCSV = () => {
    if (analysis.csvReportPath) {
      const url = `/api/analyses/${analysis.id}/report/csv`;
      window.open(url, '_blank');
    } else {
      toast({
        title: "Data Export Not Ready",
        description: "CSV export is still being generated. Please try again in a moment.",
        variant: "destructive",
      });
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `Solar Analysis for ${analysis.address}`,
        text: `Check out this solar analysis: ${analysis.panelCount} panels, ${analysis.annualKwh} kWh/year`,
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: "Link Copied",
        description: "Analysis link has been copied to your clipboard.",
      });
    }
  };

  const handleEmailShare = () => {
    const subject = `Solar Analysis Report - ${analysis.address}`;
    const body = `I wanted to share this solar analysis report with you:

Property: ${analysis.address}
Solar Panels: ${analysis.panelCount} panels
Annual Energy: ${analysis.annualKwh} kWh
Efficiency Grade: ${analysis.grade}

${analysis.aiSummary}

View the full analysis: ${window.location.href}`;

    window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Download Reports & Share</h3>
        <p className="text-gray-600">Get professional reports or share your analysis</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Download Options */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Download Reports</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={handleDownloadPDF}
              className="w-full bg-accent hover:bg-accent/90"
              disabled={!analysis.pdfReportPath}
            >
              <FileText className="h-4 w-4 mr-2" />
              Download PDF Report
            </Button>
            <Button 
              onClick={handleDownloadCSV}
              variant="outline" 
              className="w-full"
              disabled={!analysis.csvReportPath}
            >
              <DownloadIcon className="h-4 w-4 mr-2" />
              Download CSV Data
            </Button>
            
            <div className="text-xs text-gray-500 mt-2">
              <p>• PDF includes complete analysis with recommendations</p>
              <p>• CSV contains panel coordinates and technical data</p>
            </div>
          </CardContent>
        </Card>

        {/* Share Options */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Share Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button 
              onClick={handleShare}
              variant="outline" 
              className="w-full"
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share Link
            </Button>
            <Button 
              onClick={handleEmailShare}
              variant="outline" 
              className="w-full"
            >
              <Mail className="h-4 w-4 mr-2" />
              Email Report
            </Button>
            
            <div className="text-xs text-gray-500 mt-2">
              <p>• Share analysis results with clients or colleagues</p>
              <p>• Email includes summary and link to full report</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Report Preview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <p className="font-semibold text-lg text-primary">{analysis.panelCount || 0}</p>
                <p className="text-gray-600">Solar Panels</p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-lg text-primary">{analysis.annualKwh?.toLocaleString() || 0}</p>
                <p className="text-gray-600">kWh/year</p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-lg text-primary">{analysis.grade || 'N/A'}</p>
                <p className="text-gray-600">Grade</p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-lg text-primary">
                  {analysis.efficiency ? `${(analysis.efficiency * 100).toFixed(1)}%` : 'N/A'}
                </p>
                <p className="text-gray-600">Efficiency</p>
              </div>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-gray-700">
              <strong>Summary:</strong> {analysis.aiSummary || 'AI summary will be included in the report...'}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
