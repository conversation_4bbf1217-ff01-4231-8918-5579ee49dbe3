"""
Configuration settings for RoofSnap FastAPI backend
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # API Configuration
    app_name: str = "RoofSnap API"
    app_version: str = "1.0.0"
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # Database Configuration
    database_url: str = Field(
        default="postgresql://postgres:password@localhost:5432/roofsnap",
        description="PostgreSQL database URL"
    )
    
    # Google APIs
    google_api_key: str = Field(
        default="",
        description="Google Maps API key for geocoding and imagery"
    )
    
    # Gemini AI
    gemini_api_key: str = Field(
        default="",
        description="Google Gemini API key for AI summaries"
    )
    
    # CORS Settings
    cors_origins: list[str] = Field(
        default=["http://localhost:5173", "http://localhost:3000"],
        description="Allowed CORS origins"
    )
    
    # File Storage
    data_directory: str = Field(
        default="./data",
        description="Directory for storing images and reports"
    )
    
    # Solar Panel Configuration
    panel_width: float = Field(default=1.6, description="Solar panel width in meters")
    panel_height: float = Field(default=1.0, description="Solar panel height in meters")
    
    # CV Backend (for fallback if needed)
    cv_backend_url: str = Field(
        default="http://localhost:8000",
        description="Computer Vision backend URL"
    )
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings
