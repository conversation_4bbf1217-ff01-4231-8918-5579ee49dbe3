import * as fs from 'fs';
import * as path from 'path';

export interface ReportData {
  analysis: {
    address: string;
    roofArea: number;
    usableArea: number;
    panelCount: number;
    annualKwh: number;
    efficiency: number;
    grade: string;
    roofTilt: number;
    orientation: string;
    shadingLevel: string;
  };
  solarGrid: {
    zones: Array<{
      x: number;
      y: number;
      width: number;
      height: number;
      potential: string;
      efficiency: number;
      estimatedOutput: number;
    }>;
  };
  summary: string;
  recommendations: string[];
}

export class ReportGeneratorService {
  private dataDir: string;

  constructor() {
    this.dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  async generatePDFReport(data: ReportData): Promise<string> {
    // In a real implementation, this would use ReportLab or similar PDF generation library
    // For now, we'll create a simple HTML report that can be converted to PDF
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>Solar Analysis Report - ${data.analysis.address}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { text-align: center; border-bottom: 2px solid #2563EB; padding-bottom: 20px; }
        .section { margin: 30px 0; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .stat { background: #f8fafc; padding: 15px; border-radius: 8px; }
        .grade { font-size: 2em; font-weight: bold; color: #2563EB; }
        .recommendations { background: #f0f9ff; padding: 20px; border-radius: 8px; }
        .recommendations ul { margin: 10px 0; }
        .recommendations li { margin: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Solar Analysis Report</h1>
        <h2>${data.analysis.address}</h2>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
    </div>

    <div class="section">
        <h3>Executive Summary</h3>
        <p>${data.summary}</p>
    </div>

    <div class="section">
        <h3>Solar Potential Analysis</h3>
        <div class="grid">
            <div class="stat">
                <h4>Roof Area</h4>
                <p>${data.analysis.roofArea} sq ft</p>
            </div>
            <div class="stat">
                <h4>Usable Area</h4>
                <p>${data.analysis.usableArea} sq ft</p>
            </div>
            <div class="stat">
                <h4>Recommended Panels</h4>
                <p>${data.analysis.panelCount} panels</p>
            </div>
            <div class="stat">
                <h4>Annual Energy Production</h4>
                <p>${data.analysis.annualKwh} kWh</p>
            </div>
            <div class="stat">
                <h4>System Efficiency</h4>
                <p>${(data.analysis.efficiency * 100).toFixed(1)}%</p>
            </div>
            <div class="stat">
                <h4>Efficiency Grade</h4>
                <p class="grade">${data.analysis.grade}</p>
            </div>
        </div>
    </div>

    <div class="section">
        <h3>Property Details</h3>
        <div class="grid">
            <div class="stat">
                <h4>Roof Tilt</h4>
                <p>${data.analysis.roofTilt}°</p>
            </div>
            <div class="stat">
                <h4>Orientation</h4>
                <p>${data.analysis.orientation}</p>
            </div>
            <div class="stat">
                <h4>Shading Level</h4>
                <p>${data.analysis.shadingLevel}</p>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="recommendations">
            <h3>Recommendations</h3>
            <ul>
                ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
    </div>

    <div class="section">
        <h3>Panel Layout Summary</h3>
        <p>Total Zones: ${data.solarGrid.zones.length}</p>
        <p>High Potential Zones: ${data.solarGrid.zones.filter(z => z.potential === 'high').length}</p>
        <p>Medium Potential Zones: ${data.solarGrid.zones.filter(z => z.potential === 'medium').length}</p>
        <p>Low Potential Zones: ${data.solarGrid.zones.filter(z => z.potential === 'low').length}</p>
        <p>Unusable Zones: ${data.solarGrid.zones.filter(z => z.potential === 'unusable').length}</p>
    </div>
</body>
</html>
    `;

    const filename = `solar_report_${Date.now()}.html`;
    const filepath = path.join(this.dataDir, filename);
    
    fs.writeFileSync(filepath, htmlContent);
    
    return filename; // Return just the filename, not the full path
  }

  async generateCSVReport(data: ReportData): Promise<string> {
    const headers = [
      'Zone_ID',
      'X_Position',
      'Y_Position',
      'Width_M',
      'Height_M',
      'Potential_Level',
      'Efficiency_Percent',
      'Estimated_Output_W',
      'Annual_kWh'
    ];

    const rows = data.solarGrid.zones.map((zone, index) => [
      index + 1,
      zone.x.toFixed(4),
      zone.y.toFixed(4),
      zone.width.toFixed(2),
      zone.height.toFixed(2),
      zone.potential,
      (zone.efficiency * 100).toFixed(1),
      zone.estimatedOutput.toFixed(0),
      ((zone.estimatedOutput / 1000) * 4.5 * 365).toFixed(0) // Assuming 4.5 peak sun hours
    ]);

    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    const filename = `solar_data_${Date.now()}.csv`;
    const filepath = path.join(this.dataDir, filename);
    
    fs.writeFileSync(filepath, csvContent);
    
    return filename; // Return just the filename, not the full path
  }
}
