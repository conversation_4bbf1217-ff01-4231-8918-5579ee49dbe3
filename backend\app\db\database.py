"""
Database configuration and connection management
"""
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
import logging

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Configure database URL based on type
if settings.database_url.startswith("postgresql"):
    # PostgreSQL/Supabase configuration
    async_database_url = settings.database_url.replace("postgresql://", "postgresql+asyncpg://")
    engine = create_async_engine(
        async_database_url,
        echo=settings.debug,
        future=True,
        pool_pre_ping=True,  # Enable connection health checks
        pool_recycle=300     # Recycle connections every 5 minutes
    )
else:
    # SQLite configuration
    async_database_url = settings.database_url.replace("sqlite:///", "sqlite+aiosqlite:///")
    engine = create_async_engine(
        async_database_url,
        echo=settings.debug,
        future=True,
        connect_args={"check_same_thread": False}
    )

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# Create base class for models
Base = declarative_base()


async def get_db() -> AsyncSession:
    """
    Dependency to get database session
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def init_db():
    """
    Initialize database tables
    """
    try:
        async with engine.begin() as conn:
            # Import models to ensure they're registered
            from app.db.models import Analysis, User
            
            # Create all tables
            await conn.run_sync(Base.metadata.create_all)
            
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def close_db():
    """
    Close database connections
    """
    await engine.dispose()
