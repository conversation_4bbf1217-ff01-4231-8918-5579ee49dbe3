import { <PERSON><PERSON><PERSON><PERSON>, UserCircle, Zap, Sun } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import logoPath from "@assets/ChatGPT Image Jul 3, 2025, 11_46_24 AM_1751523445921.png";

export default function Header() {
  return (
    <header className="relative bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 bg-white shadow-lg border-b">
      {/* Animated background - only in dark mode */}
      <div className="absolute inset-0 dark:block hidden">
        <div className="absolute inset-0 animated-gradient opacity-30"></div>
        
        {/* Floating particles effect */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-4 left-10 w-2 h-2 bg-white/10 rounded-full float"></div>
          <div className="absolute top-8 right-20 w-3 h-3 bg-white/8 rounded-full float-delayed"></div>
          <div className="absolute top-6 left-1/3 w-1 h-1 bg-white/15 rounded-full float"></div>
          <div className="absolute bottom-4 right-10 w-2 h-2 bg-white/10 rounded-full float-delayed"></div>
          <div className="absolute bottom-6 left-1/4 w-1 h-1 bg-white/12 rounded-full float"></div>
        </div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <img 
                src={logoPath} 
                alt="RoofSnap Logo" 
                className="h-8 w-8"
              />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-foreground bg-gradient-to-r from-blue-600 to-purple-600 dark:from-yellow-400 dark:to-orange-400 bg-clip-text text-transparent">
                RoofSnap
              </h1>
              <p className="text-muted-foreground text-sm font-medium">AI-Powered Solar Analysis Platform</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center space-x-2 bg-card border rounded-lg px-3 py-2 hover:bg-accent transition-all duration-300 group">
                <Zap className="w-4 h-4 text-yellow-500 group-hover:scale-110 transition-transform" />
                <span className="text-card-foreground text-sm font-medium">Real-time Analysis</span>
              </div>
              <div className="flex items-center space-x-2 bg-card border rounded-lg px-3 py-2 hover:bg-accent transition-all duration-300 group">
                <Sun className="w-4 h-4 text-orange-500 group-hover:scale-110 transition-transform" />
                <span className="text-card-foreground text-sm font-medium">Solar Optimization</span>
              </div>
            </div>
            
            <div className="bg-card border rounded-lg px-3 py-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full pulse-soft"></div>
                <span className="text-card-foreground text-xs font-medium">System Online</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Button variant="ghost" size="sm">
                <HelpCircle className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm">
                <UserCircle className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
