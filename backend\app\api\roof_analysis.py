"""
Specialized roof analysis API endpoints
"""
from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
import logging
from typing import Dict, Any

from app.db.database import get_db
try:
    from app.services.vision import VisionService
except ImportError:
    # Fallback to mock for localhost testing
    from app.services.vision_mock import MockVisionService as VisionService
from app.services.ai_analysis import AIAnalysisService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/roof", tags=["roof-analysis"])

# Initialize services
vision_service = VisionService()
ai_analysis_service = AIAnalysisService()


@router.post("/analyze-image")
async def analyze_roof_image(
    file: UploadFile = File(...),
    latitude: float = 37.4419,
    longitude: float = -122.1430,
    zoom: int = 19
):
    """
    Analyze roof from uploaded satellite image
    
    Args:
        file: Uploaded image file
        latitude: Latitude for pixel-to-meter conversion
        longitude: Longitude for context
        zoom: Zoom level for accurate measurements
        
    Returns:
        Complete roof analysis results
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="File must be an image"
            )
        
        # Read image data
        image_data = await file.read()
        
        # Perform computer vision analysis
        result = await vision_service.analyze_roof_from_image(
            image_data, latitude, longitude, zoom
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=f"Analysis failed: {result.get('error')}"
            )
        
        return {
            "success": True,
            "roof_detection": result["roof_detection"],
            "solar_grid": result["solar_grid"],
            "solar_analysis": result["solar_analysis"],
            "visualization": result.get("visualization", "")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing roof image: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze roof image"
        )


@router.post("/analyze-coordinates")
async def analyze_roof_coordinates(
    latitude: float,
    longitude: float,
    address: str = "",
    zoom: int = 19
):
    """
    Analyze roof from coordinates by fetching satellite imagery
    
    Args:
        latitude: Property latitude
        longitude: Property longitude
        address: Optional address for context
        zoom: Zoom level for satellite image
        
    Returns:
        Complete roof analysis results
    """
    try:
        # Use the AI analysis service to fetch imagery and analyze
        from app.services.imagery import ImageryService
        
        imagery_service = ImageryService()
        
        # Fetch satellite image
        imagery_result = await imagery_service.fetch_satellite_image(
            latitude, longitude, zoom
        )
        
        # Read the saved image
        with open(imagery_result["local_path"], 'rb') as f:
            image_data = f.read()
        
        # Perform analysis
        result = await vision_service.analyze_roof_from_image(
            image_data, latitude, longitude, zoom
        )
        
        if not result.get("success"):
            raise HTTPException(
                status_code=500,
                detail=f"Analysis failed: {result.get('error')}"
            )
        
        return {
            "success": True,
            "satellite_image_url": imagery_result["data_url"],
            "roof_detection": result["roof_detection"],
            "solar_grid": result["solar_grid"],
            "solar_analysis": result["solar_analysis"],
            "visualization": result.get("visualization", "")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing roof coordinates: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to analyze roof coordinates"
        )


@router.get("/detection-methods")
async def get_detection_methods():
    """
    Get available roof detection methods and their descriptions
    """
    return {
        "methods": [
            {
                "name": "edge_detection",
                "description": "Uses Canny edge detection and contour analysis",
                "accuracy": "Good for clear roof boundaries",
                "default": True
            },
            {
                "name": "color_segmentation", 
                "description": "Segments roof based on color similarity",
                "accuracy": "Good for uniform roof colors",
                "default": False
            }
        ],
        "panel_specifications": {
            "width_meters": vision_service.panel_placer.panel_width,
            "height_meters": vision_service.panel_placer.panel_height,
            "area_m2": vision_service.panel_placer.panel_area,
            "spacing_meters": vision_service.panel_placer.panel_spacing
        }
    }


@router.post("/validate-roof-polygon")
async def validate_roof_polygon(polygon_data: Dict[str, Any]):
    """
    Validate a manually drawn roof polygon
    
    Args:
        polygon_data: Dictionary containing polygon coordinates
        
    Returns:
        Validation results and area calculations
    """
    try:
        from shapely.geometry import Polygon
        
        polygon_coords = polygon_data.get("coordinates", [])
        
        if len(polygon_coords) < 3:
            raise HTTPException(
                status_code=400,
                detail="Polygon must have at least 3 points"
            )
        
        # Create Shapely polygon
        polygon = Polygon(polygon_coords)
        
        if not polygon.is_valid:
            raise HTTPException(
                status_code=400,
                detail="Invalid polygon geometry"
            )
        
        # Calculate area and other metrics
        area_pixels = polygon.area
        perimeter_pixels = polygon.length
        
        # Convert to real-world measurements (assuming zoom level)
        zoom = polygon_data.get("zoom", 19)
        area_m2 = vision_service.panel_placer.pixel_to_meters(area_pixels, zoom)
        
        return {
            "valid": True,
            "area_pixels": area_pixels,
            "area_m2": area_m2,
            "area_sqft": area_m2 * 10.764,
            "perimeter_pixels": perimeter_pixels,
            "bounds": polygon.bounds,
            "centroid": [polygon.centroid.x, polygon.centroid.y]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating polygon: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to validate polygon"
        )
