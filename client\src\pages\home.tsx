import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Search, MapPin, Camera, FileText, Sun, Building2, Zap, TrendingUp } from "lucide-react";
import AddressInputBar from "@/components/address-input-bar";
import MapOverlayViewer from "@/components/map-overlay-viewer";
import StreetViewPanel from "@/components/street-view-panel";
import AnalysisSummary from "@/components/analysis-summary";
import ProgressTracker from "@/components/progress-tracker";
import Header from "@/components/header";
import { useQuery } from "@tanstack/react-query";
import type { Analysis } from "@shared/schema";

export default function Home() {
  const [currentAnalysisId, setCurrentAnalysisId] = useState<number | null>(null);
  const [analysisStatus, setAnalysisStatus] = useState<'idle' | 'processing' | 'completed' | 'failed'>('idle');
  const [activeTab, setActiveTab] = useState("address");

  const { data: currentAnalysis, isLoading: analysisLoading } = useQuery({
    queryKey: [`/api/analyses/${currentAnalysisId}`],
    enabled: !!currentAnalysisId,
    refetchInterval: analysisStatus === 'processing' ? 2000 : false,
  });

  const handleAnalysisStart = (analysisId: number) => {
    setCurrentAnalysisId(analysisId);
    setAnalysisStatus('processing');
    setActiveTab("analysis");
  };

  // Update analysis status based on current analysis
  if (currentAnalysis && (currentAnalysis as Analysis).status !== analysisStatus) {
    setAnalysisStatus((currentAnalysis as Analysis).status as any);
    if ((currentAnalysis as Analysis).status === 'completed') {
      setActiveTab("analysis");
    }
  }

  const isAnalysisReady = currentAnalysis && analysisStatus === 'completed';

  return (
    <div className="min-h-screen bg-background relative overflow-hidden">
      {/* Animated background elements - only in dark mode */}
      <div className="absolute inset-0 overflow-hidden dark:block hidden">
        {/* Floating orbs */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl float"></div>
        <div className="absolute top-3/4 right-1/4 w-96 h-96 bg-purple-500/5 rounded-full blur-3xl float-delayed"></div>
        <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-indigo-500/5 rounded-full blur-3xl float"></div>
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZGVmcz48cGF0dGVybiBpZD0iZ3JpZCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBwYXR0ZXJuVW5pdHM9InVzZXJTcGFjZU9uVXNlIj48cGF0aCBkPSJNIDQwIDAgTCAwIDAgMCA0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJyZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMDIpIiBzdHJva2Utd2lkdGg9IjEiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjZ3JpZCkiLz48L3N2Zz4=')] opacity-30"></div>
      </div>
      
      <Header />
      
      <div className="flex-1 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Main Tabs Navigation */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8 bg-card/80 dark:glass rounded-xl shadow-lg border dark:border-white/20 border-border backdrop-blur-sm p-1">
              <TabsTrigger value="address" className="flex items-center gap-2 text-sm font-medium transition-all">
                <Search className="h-4 w-4" />
                Address Input
              </TabsTrigger>
              <TabsTrigger 
                value="analysis" 
                className="flex items-center gap-2 text-sm font-medium transition-all"
                disabled={!currentAnalysisId}
              >
                <MapPin className="h-4 w-4" />
                Image & Overlay
              </TabsTrigger>
              <TabsTrigger 
                value="streetview" 
                className="flex items-center gap-2 text-sm font-medium transition-all"
                disabled={!isAnalysisReady}
              >
                <Camera className="h-4 w-4" />
                Street Views
              </TabsTrigger>
              <TabsTrigger 
                value="summary" 
                className="flex items-center gap-2 text-sm font-medium transition-all"
                disabled={!isAnalysisReady}
              >
                <FileText className="h-4 w-4" />
                Analysis Summary
              </TabsTrigger>
            </TabsList>

            {/* Tab Content */}
            <div className="min-h-[600px]">
              {/* Address Input Tab */}
              <TabsContent value="address" className="space-y-6 animate-in slide-in-from-right-5 duration-300">
                <div className="text-center space-y-4 mb-8">
                  <div className="flex justify-center items-center gap-3 mb-4">
                    <Sun className="h-12 w-12 text-yellow-500" />
                    <Building2 className="h-10 w-10 text-blue-600" />
                    <Zap className="h-8 w-8 text-green-500" />
                  </div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    Professional Solar Analysis Platform
                  </h1>
                  <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Get comprehensive roof analysis with AI-powered solar potential assessment and professional reporting for solar installations.
                  </p>
                </div>
                <AddressInputBar onAnalysisStart={handleAnalysisStart} />
              </TabsContent>

              {/* Image & Overlay Tab */}
              <TabsContent value="analysis" className="animate-in slide-in-from-right-5 duration-300">
                {analysisStatus === 'processing' && <ProgressTracker analysis={currentAnalysis as Analysis} />}
                {isAnalysisReady && <MapOverlayViewer analysis={currentAnalysis as Analysis} />}
              </TabsContent>

              {/* Street Views Tab */}
              <TabsContent value="streetview" className="animate-in slide-in-from-right-5 duration-300">
                {isAnalysisReady && <StreetViewPanel analysis={currentAnalysis as Analysis} />}
              </TabsContent>

              {/* Analysis Summary Tab */}
              <TabsContent value="summary" className="animate-in slide-in-from-right-5 duration-300">
                {isAnalysisReady && <AnalysisSummary analysis={currentAnalysis as Analysis} />}
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
