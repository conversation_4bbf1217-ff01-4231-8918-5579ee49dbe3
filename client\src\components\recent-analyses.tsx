import { Home, Calendar, TrendingUp } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import type { Analysis } from "@shared/schema";

interface RecentAnalysesProps {
  analyses: Analysis[];
  loading: boolean;
  onAnalysisSelect: (analysis: Analysis) => void;
}

export default function RecentAnalyses({ analyses, loading, onAnalysisSelect }: RecentAnalysesProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Analyses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border rounded-lg p-4 space-y-3">
                <Skeleton className="h-4 w-3/4" />
                <div className="grid grid-cols-2 gap-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (analyses.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Recent Analyses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No analyses yet. Start by analyzing your first property!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getGradeColor = (grade: string) => {
    if (grade?.startsWith('A')) return 'bg-accent';
    if (grade?.startsWith('B')) return 'bg-warning';
    if (grade?.startsWith('C')) return 'bg-orange-500';
    return 'bg-destructive';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Recent Analyses</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {analyses.map((analysis) => (
            <div 
              key={analysis.id}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => onAnalysisSelect(analysis)}
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center flex-1 min-w-0">
                  <Home className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                  <span className="text-sm font-medium truncate">{analysis.address}</span>
                </div>
                <Badge 
                  className={`ml-2 text-white ${getGradeColor(analysis.grade || '')}`}
                  variant="secondary"
                >
                  {analysis.grade || 'N/A'}
                </Badge>
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="text-gray-500">Panels:</span>
                  <span className="font-medium ml-1">{analysis.panelCount || 0}</span>
                </div>
                <div>
                  <span className="text-gray-500">kWh/year:</span>
                  <span className="font-medium ml-1">{analysis.annualKwh?.toLocaleString() || 0}</span>
                </div>
                <div>
                  <span className="text-gray-500">Status:</span>
                  <span className={`font-medium ml-1 ${
                    analysis.status === 'completed' ? 'text-accent' : 
                    analysis.status === 'processing' ? 'text-warning' : 
                    analysis.status === 'failed' ? 'text-destructive' : 
                    'text-gray-500'
                  }`}>
                    {analysis.status === 'completed' ? 'Complete' : 
                     analysis.status === 'processing' ? 'Processing' : 
                     analysis.status === 'failed' ? 'Failed' : 'Pending'}
                  </span>
                </div>
                <div>
                  <span className="text-gray-500">Date:</span>
                  <span className="font-medium ml-1">
                    {analysis.createdAt ? new Date(analysis.createdAt).toLocaleDateString() : 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
