export interface GeminiSummaryResult {
  summary: string;
  recommendations: string[];
}

export class GeminiService {
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_AI_API_KEY || "";
    if (!this.apiKey) {
      throw new Error("Gemini API key is required");
    }
  }

  async generateSummary(analysisData: {
    address: string;
    roofArea: number;
    usableArea: number;
    panelCount: number;
    annualKwh: number;
    efficiency: number;
    grade: string;
    roofTilt: number;
    orientation: string;
    shadingLevel: string;
  }): Promise<GeminiSummaryResult> {
    
    const prompt = `
Generate a professional solar analysis summary for the following property:

Address: ${analysisData.address}
Roof Area: ${analysisData.roofArea} sq ft
Usable Area: ${analysisData.usableArea} sq ft
Recommended Panels: ${analysisData.panelCount}
Annual Energy Production: ${analysisData.annualKwh} kWh
System Efficiency: ${(analysisData.efficiency * 100).toFixed(1)}%
Efficiency Grade: ${analysisData.grade}
Roof Tilt: ${analysisData.roofTilt}°
Orientation: ${analysisData.orientation}
Shading Level: ${analysisData.shadingLevel}

Please provide:
1. A concise 2-3 sentence summary of the solar potential
2. 3-5 specific recommendations for optimal solar installation

Format your response as JSON with 'summary' and 'recommendations' fields.
`;

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }]
        })
      });

      if (!response.ok) {
        throw new Error(`Gemini API error: ${response.statusText}`);
      }

      const data = await response.json();
      const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
      
      if (!generatedText) {
        throw new Error('No content generated by Gemini');
      }

      // Try to parse as JSON, fallback to formatted text
      try {
        const parsed = JSON.parse(generatedText);
        return {
          summary: parsed.summary || generatedText,
          recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : [generatedText]
        };
      } catch {
        // Fallback: create structured response from text
        const lines = generatedText.split('\n').filter(line => line.trim());
        const summary = lines.slice(0, 2).join(' ');
        const recommendations = lines.slice(2).filter(line => line.includes('recommend') || line.includes('suggest') || line.includes('consider'));
        
        return {
          summary: summary || `This property shows ${analysisData.grade} solar potential with ${analysisData.annualKwh} kWh annual production capacity.`,
          recommendations: recommendations.length > 0 ? recommendations : [
            'Consider high-efficiency panels for maximum output',
            'Optimize panel placement to minimize shading',
            'Regular maintenance will ensure optimal performance'
          ]
        };
      }
    } catch (error) {
      console.error('Gemini API error:', error);
      
      // Fallback summary generation
      return {
        summary: `This ${analysisData.orientation.toLowerCase()} property demonstrates ${analysisData.grade} solar potential with an estimated ${analysisData.annualKwh} kWh annual energy production from ${analysisData.panelCount} recommended panels.`,
        recommendations: [
          `With ${analysisData.shadingLevel.toLowerCase()} shading levels, this roof is ${analysisData.grade === 'A+' || analysisData.grade === 'A' ? 'excellent' : analysisData.grade.startsWith('B') ? 'good' : 'suitable'} for solar installation`,
          `The ${analysisData.roofTilt}° roof tilt is ${Math.abs(analysisData.roofTilt - 30) < 10 ? 'optimal' : 'acceptable'} for solar energy capture`,
          `Consider ${analysisData.efficiency < 0.8 ? 'high-efficiency panels to maximize limited space' : 'standard panels for cost-effective installation'}`,
          'Schedule professional installation assessment for detailed system design'
        ]
      };
    }
  }
}
