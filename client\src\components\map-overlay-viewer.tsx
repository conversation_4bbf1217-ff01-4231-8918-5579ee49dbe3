import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ZoomIn, ZoomOut, Maximize2, RotateCcw, MapPin, Sun, Activity, TrendingUp } from "lucide-react";
import type { Analysis } from "@shared/schema";

interface MapOverlayViewerProps {
  analysis: Analysis;
}

export default function MapOverlayViewer({ analysis }: MapOverlayViewerProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showOverlay, setShowOverlay] = useState(true);

  const renderSolarGrid = () => {
    if (!analysis.solarGrid?.zones || analysis.solarGrid.zones.length === 0) return null;

    return (
      <svg className="absolute inset-0 w-full h-full pointer-events-none" viewBox="0 0 1 1" preserveAspectRatio="none">
        {analysis.solarGrid.zones.map((zone, index) => {
          const x = zone.x;
          const y = zone.y;
          const width = zone.width;
          const height = zone.height;
          
          let color = '';
          let opacity = 0.7;
          switch (zone.potential) {
            case 'high':
              color = '#22c55e'; // Green
              break;
            case 'medium':
              color = '#eab308'; // Yellow
              break;
            case 'low':
              color = '#f97316'; // Orange
              break;
            case 'unusable':
              color = '#ef4444'; // Red
              break;
            default:
              color = '#6b7280'; // Gray
          }

          return (
            <rect
              key={index}
              x={x}
              y={y}
              width={width}
              height={height}
              fill={color}
              fillOpacity={opacity}
              stroke={color}
              strokeWidth="0.0005"
            />
          );
        })}
      </svg>
    );
  };

  const stats = [
    {
      icon: MapPin,
      label: "Roof Area",
      value: `${analysis.roofArea?.toLocaleString() || 0} sq ft`,
      color: "text-blue-600",
      bg: "bg-blue-50"
    },
    {
      icon: Sun,
      label: "Usable Area",
      value: `${analysis.usableArea?.toLocaleString() || 0} sq ft`,
      color: "text-green-600",
      bg: "bg-green-50"
    },
    {
      icon: Activity,
      label: "Solar Panels",
      value: `${analysis.panelCount || 0} panels`,
      color: "text-orange-600",
      bg: "bg-orange-50"
    },
    {
      icon: TrendingUp,
      label: "Annual kWh",
      value: `${analysis.annualKwh?.toLocaleString() || 0} kWh`,
      color: "text-purple-600",
      bg: "bg-purple-50"
    }
  ];

  const zoneStats = [
    { 
      label: "High Energy", 
      count: analysis.solarGrid?.zones?.filter(z => z.potential === 'high').length || 0,
      color: "bg-green-500"
    },
    { 
      label: "Medium Energy", 
      count: analysis.solarGrid?.zones?.filter(z => z.potential === 'medium').length || 0,
      color: "bg-yellow-500"
    },
    { 
      label: "Low Energy", 
      count: analysis.solarGrid?.zones?.filter(z => z.potential === 'low').length || 0,
      color: "bg-orange-500"
    },
    { 
      label: "Unusable", 
      count: analysis.solarGrid?.zones?.filter(z => z.potential === 'unusable').length || 0,
      color: "bg-red-500"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h2 className="text-3xl font-bold text-gray-800">Solar Analysis Results</h2>
        <p className="text-lg text-gray-600">{analysis.address}</p>
        <div className="flex justify-center">
          <Badge variant="outline" className="text-lg px-4 py-1">
            Grade: {analysis.grade || 'Calculating...'}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Map Display */}
        <div className="lg:col-span-3">
          <Card className="shadow-xl">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-6 w-6 text-blue-600" />
                  Satellite View with Solar Overlay
                </CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowOverlay(!showOverlay)}
                    className={showOverlay ? "bg-blue-50 border-blue-200" : ""}
                  >
                    {showOverlay ? "Hide" : "Show"} Overlay
                  </Button>
                  <Button variant="outline" size="sm">
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Maximize2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="relative aspect-video bg-gray-200 rounded-b-lg overflow-hidden">
                {analysis.satelliteImageUrl ? (
                  <img 
                    src={analysis.satelliteImageUrl}
                    alt="Satellite view of property"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-gray-500">Loading satellite image...</p>
                    </div>
                  </div>
                )}
                
                {/* Solar Panel Grid Overlay */}
                {showOverlay && renderSolarGrid()}
              </div>
            </CardContent>
          </Card>

          {/* Solar Zone Legend */}
          <Card className="mt-4">
            <CardContent className="p-4">
              <h4 className="font-semibold mb-3 text-gray-800">Solar Energy Potential Legend</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {zoneStats.map((zone, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <div className={`w-4 h-4 ${zone.color} rounded`}></div>
                    <div>
                      <span className="text-sm font-medium">{zone.label}</span>
                      <span className="text-xs text-gray-500 ml-1">({zone.count} zones)</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Stats Panel */}
        <div className="space-y-4">
          {/* Key Statistics */}
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <div className={`w-12 h-12 ${stat.bg} rounded-xl flex items-center justify-center`}>
                      <Icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">{stat.label}</p>
                      <p className="text-lg font-bold text-gray-800">{stat.value}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* System Efficiency */}
          <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
            <CardContent className="p-4 text-center">
              <h4 className="font-semibold text-gray-800 mb-2">System Efficiency</h4>
              <div className="text-3xl font-bold text-blue-600">
                {analysis.efficiency ? `${(analysis.efficiency * 100).toFixed(1)}%` : 'Calculating...'}
              </div>
              <p className="text-sm text-gray-600 mt-1">Overall Performance Rating</p>
            </CardContent>
          </Card>

          {/* Property Details */}
          <Card>
            <CardContent className="p-4">
              <h4 className="font-semibold text-gray-800 mb-3">Property Details</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Roof Tilt:</span>
                  <span className="font-medium">{analysis.roofTilt || 'N/A'}°</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Orientation:</span>
                  <span className="font-medium">{analysis.orientation || 'N/A'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Shading:</span>
                  <span className="font-medium">{analysis.shadingLevel || 'N/A'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}