#!/usr/bin/env python3
"""
Run the FastAPI CV backend server
"""
import uvicorn
import os
import sys

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    # Check if Google API key is set
    if not os.getenv("GOOGLE_API_KEY"):
        print("Warning: GOOGLE_API_KEY environment variable not set")
        print("The CV backend will still run but satellite image fetching will fail")
    
    print("Starting RoofSnap CV Backend on port 8000...")
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )