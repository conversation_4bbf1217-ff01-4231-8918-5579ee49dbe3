"""
Solar calculations and mathematical models for energy production
"""
import math
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class SolarMathService:
    """Advanced solar calculations and energy modeling"""
    
    def __init__(self):
        # Solar constants
        self.solar_constant = 1361  # W/m² (solar irradiance at top of atmosphere)
        self.panel_efficiency = 0.20  # 20% typical silicon panel efficiency
        self.system_losses = 0.15  # 15% system losses (inverter, wiring, etc.)
        self.degradation_rate = 0.005  # 0.5% annual degradation
        
    def calculate_sun_position(self, latitude: float, longitude: float, date: datetime) -> Dict[str, float]:
        """
        Calculate sun position (elevation and azimuth) for given location and time
        
        Args:
            latitude: Location latitude in degrees
            longitude: Location longitude in degrees  
            date: Date and time for calculation
            
        Returns:
            Dictionary with sun elevation and azimuth angles
        """
        # Convert to radians
        lat_rad = math.radians(latitude)
        
        # Day of year
        day_of_year = date.timetuple().tm_yday
        
        # Solar declination angle
        declination = math.radians(23.45) * math.sin(math.radians(360 * (284 + day_of_year) / 365))
        
        # Hour angle (solar time)
        hour_angle = math.radians(15 * (date.hour - 12))
        
        # Solar elevation angle
        elevation = math.asin(
            math.sin(declination) * math.sin(lat_rad) + 
            math.cos(declination) * math.cos(lat_rad) * math.cos(hour_angle)
        )
        
        # Solar azimuth angle
        azimuth = math.atan2(
            math.sin(hour_angle),
            math.cos(hour_angle) * math.sin(lat_rad) - math.tan(declination) * math.cos(lat_rad)
        )
        
        return {
            "elevation": math.degrees(elevation),
            "azimuth": math.degrees(azimuth),
            "declination": math.degrees(declination)
        }
    
    def calculate_solar_irradiance(
        self, 
        latitude: float, 
        longitude: float,
        panel_tilt: float = 30,
        panel_azimuth: float = 180,  # South-facing
        date: datetime = None
    ) -> Dict[str, float]:
        """
        Calculate solar irradiance on tilted panel surface
        
        Args:
            latitude: Location latitude
            longitude: Location longitude
            panel_tilt: Panel tilt angle (0 = horizontal, 90 = vertical)
            panel_azimuth: Panel azimuth (180 = south-facing)
            date: Date for calculation (defaults to current)
            
        Returns:
            Irradiance values and solar geometry
        """
        if date is None:
            date = datetime.now()
        
        sun_pos = self.calculate_sun_position(latitude, longitude, date)
        
        # Convert angles to radians
        sun_elevation_rad = math.radians(sun_pos["elevation"])
        sun_azimuth_rad = math.radians(sun_pos["azimuth"])
        panel_tilt_rad = math.radians(panel_tilt)
        panel_azimuth_rad = math.radians(panel_azimuth)
        
        # Calculate angle of incidence (angle between sun and panel normal)
        cos_incidence = (
            math.sin(sun_elevation_rad) * math.cos(panel_tilt_rad) +
            math.cos(sun_elevation_rad) * math.sin(panel_tilt_rad) * 
            math.cos(sun_azimuth_rad - panel_azimuth_rad)
        )
        
        # Ensure non-negative
        cos_incidence = max(0, cos_incidence)
        
        # Direct normal irradiance (simplified atmospheric model)
        air_mass = 1 / (math.sin(sun_elevation_rad) + 0.50572 * (sun_pos["elevation"] + 6.07995)**(-1.6364))
        direct_normal = self.solar_constant * 0.7**(air_mass**0.678)
        
        # Irradiance on tilted surface
        direct_irradiance = direct_normal * cos_incidence
        
        # Add diffuse component (simplified)
        diffuse_irradiance = 0.1 * direct_normal * (1 + math.cos(panel_tilt_rad)) / 2
        
        # Total irradiance
        total_irradiance = direct_irradiance + diffuse_irradiance
        
        return {
            "total_irradiance": max(0, total_irradiance),  # W/m²
            "direct_irradiance": max(0, direct_irradiance),
            "diffuse_irradiance": diffuse_irradiance,
            "sun_elevation": sun_pos["elevation"],
            "sun_azimuth": sun_pos["azimuth"],
            "incidence_angle": math.degrees(math.acos(cos_incidence)) if cos_incidence > 0 else 90,
            "air_mass": air_mass
        }
    
    def calculate_annual_production(
        self,
        panel_area_m2: float,
        latitude: float,
        longitude: float,
        panel_tilt: float = 30,
        panel_azimuth: float = 180
    ) -> Dict[str, Any]:
        """
        Calculate annual energy production for given panel configuration
        
        Args:
            panel_area_m2: Total panel area in square meters
            latitude: Location latitude
            longitude: Location longitude
            panel_tilt: Panel tilt angle
            panel_azimuth: Panel azimuth angle
            
        Returns:
            Annual production estimates and monthly breakdown
        """
        monthly_production = []
        total_annual_kwh = 0
        
        # Calculate for each month
        for month in range(1, 13):
            # Use 15th of each month as representative day
            date = datetime(2024, month, 15, 12, 0)  # Solar noon
            
            # Get irradiance for this month
            irradiance_data = self.calculate_solar_irradiance(
                latitude, longitude, panel_tilt, panel_azimuth, date
            )
            
            # Estimate daily irradiation (kWh/m²/day)
            # Simplified: assume 6 hours of effective sunlight
            daily_irradiation = irradiance_data["total_irradiance"] * 6 / 1000
            
            # Monthly energy production
            days_in_month = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month - 1]
            monthly_kwh = (
                daily_irradiation * 
                panel_area_m2 * 
                self.panel_efficiency * 
                (1 - self.system_losses) * 
                days_in_month
            )
            
            monthly_production.append({
                "month": month,
                "month_name": date.strftime("%B"),
                "daily_irradiation": round(daily_irradiation, 2),
                "monthly_kwh": round(monthly_kwh, 1),
                "sun_elevation": round(irradiance_data["sun_elevation"], 1)
            })
            
            total_annual_kwh += monthly_kwh
        
        # Calculate financial metrics
        electricity_rate = 0.12  # $/kWh
        annual_savings = total_annual_kwh * electricity_rate
        
        return {
            "annual_kwh": round(total_annual_kwh, 0),
            "annual_savings": round(annual_savings, 0),
            "monthly_breakdown": monthly_production,
            "system_specs": {
                "panel_area_m2": panel_area_m2,
                "panel_efficiency": self.panel_efficiency,
                "system_losses": self.system_losses,
                "panel_tilt": panel_tilt,
                "panel_azimuth": panel_azimuth
            },
            "performance_metrics": {
                "specific_yield": round(total_annual_kwh / panel_area_m2, 1),  # kWh/m²/year
                "capacity_factor": round(total_annual_kwh / (panel_area_m2 * self.panel_efficiency * 1000 * 8760), 3),
                "peak_power_kw": round(panel_area_m2 * self.panel_efficiency, 2)
            }
        }
    
    def optimize_panel_tilt(self, latitude: float) -> float:
        """
        Calculate optimal panel tilt angle for maximum annual production
        
        Args:
            latitude: Location latitude
            
        Returns:
            Optimal tilt angle in degrees
        """
        # Simple rule: optimal tilt ≈ latitude for fixed installations
        # Adjust slightly based on latitude
        if abs(latitude) < 25:
            return abs(latitude) + 5
        elif abs(latitude) < 50:
            return abs(latitude)
        else:
            return abs(latitude) - 5
    
    def calculate_shading_impact(
        self,
        panels: List[Dict[str, Any]],
        obstruction_height: float,
        obstruction_distance: float,
        latitude: float
    ) -> Dict[str, Any]:
        """
        Calculate impact of shading on panel performance
        
        Args:
            panels: List of panel configurations
            obstruction_height: Height of obstruction (meters)
            obstruction_distance: Distance to obstruction (meters)
            latitude: Location latitude
            
        Returns:
            Shading analysis results
        """
        # Calculate shadow angle
        shadow_angle = math.degrees(math.atan(obstruction_height / obstruction_distance))
        
        # Minimum sun elevation to avoid shading
        min_sun_elevation = shadow_angle
        
        # Estimate shading hours per day (simplified)
        # This is a rough approximation - real calculation would need hourly analysis
        if min_sun_elevation > 60:
            shading_factor = 0.8  # Heavy shading
        elif min_sun_elevation > 30:
            shading_factor = 0.6  # Moderate shading
        elif min_sun_elevation > 15:
            shading_factor = 0.3  # Light shading
        else:
            shading_factor = 0.1  # Minimal shading
        
        # Apply shading to panels
        shaded_panels = []
        for panel in panels:
            shaded_panel = panel.copy()
            shaded_panel["efficiency"] *= (1 - shading_factor)
            shaded_panel["estimated_output"] *= (1 - shading_factor)
            shaded_panel["shading_factor"] = shading_factor
            shaded_panels.append(shaded_panel)
        
        return {
            "shadow_angle": round(shadow_angle, 1),
            "min_sun_elevation": round(min_sun_elevation, 1),
            "shading_factor": round(shading_factor, 2),
            "shaded_panels": shaded_panels,
            "production_loss": round(shading_factor * 100, 1)  # Percentage loss
        }
