import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import type { Analysis } from "@shared/schema";

interface StreetViewsProps {
  analysis: Analysis;
}

export default function StreetViews({ analysis }: StreetViewsProps) {
  const directions = ['North', 'East', 'South', 'West'];
  const streetViews = analysis.streetViewUrls || [];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">Multi-Angle Street Views</h3>
        <p className="text-gray-600">View the property from different angles to understand the roof structure</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {streetViews.map((imageUrl, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="text-base flex items-center justify-between">
                <span>{directions[index]} View</span>
                <Badge variant="outline">{index + 1} of {streetViews.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden">
                <img 
                  src={imageUrl}
                  alt={`Street view from ${directions[index]}`}
                  className="w-full h-full object-cover"
                />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      {streetViews.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <p className="text-gray-500">Street view images are being processed...</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
