"""
Geocoding service using Google Geocoding API
"""
import aiohttp
import logging
from typing import Dict, Any
from fastapi import HTTPException

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class GeocodingService:
    """Service for geocoding addresses and getting elevation data"""
    
    def __init__(self):
        self.api_key = settings.google_api_key
        if not self.api_key:
            logger.warning("Google API key not configured - geocoding will fail")
    
    async def geocode_address(self, address: str) -> Dict[str, Any]:
        """
        Geocode an address to get latitude, longitude, and formatted address
        
        Args:
            address: The address to geocode
            
        Returns:
            Dict containing latitude, longitude, and formattedAddress
            
        Raises:
            HTTPException: If geocoding fails
        """
        if not self.api_key:
            raise HTTPException(
                status_code=500,
                detail="Google API key not configured"
            )
        
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            "address": address,
            "key": self.api_key
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Geocoding API request failed: {response.status}"
                        )
                    
                    data = await response.json()
                    
                    if data.get("status") != "OK":
                        error_msg = data.get("error_message", "Unknown error")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Geocoding failed: {data.get('status')} - {error_msg}"
                        )
                    
                    if not data.get("results"):
                        raise HTTPException(
                            status_code=404,
                            detail="No results found for the given address"
                        )
                    
                    result = data["results"][0]
                    location = result["geometry"]["location"]
                    
                    return {
                        "latitude": location["lat"],
                        "longitude": location["lng"],
                        "formattedAddress": result["formatted_address"]
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error during geocoding: {e}")
            raise HTTPException(
                status_code=500,
                detail="Network error during geocoding"
            )
        except Exception as e:
            logger.error(f"Unexpected error during geocoding: {e}")
            raise HTTPException(
                status_code=500,
                detail="Unexpected error during geocoding"
            )
    
    async def get_elevation(self, latitude: float, longitude: float) -> float:
        """
        Get elevation for given coordinates
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Elevation in meters
            
        Raises:
            HTTPException: If elevation API fails
        """
        if not self.api_key:
            raise HTTPException(
                status_code=500,
                detail="Google API key not configured"
            )
        
        url = "https://maps.googleapis.com/maps/api/elevation/json"
        params = {
            "locations": f"{latitude},{longitude}",
            "key": self.api_key
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Elevation API request failed: {response.status}"
                        )
                    
                    data = await response.json()
                    
                    if data.get("status") != "OK":
                        error_msg = data.get("error_message", "Unknown error")
                        raise HTTPException(
                            status_code=400,
                            detail=f"Elevation API failed: {data.get('status')} - {error_msg}"
                        )
                    
                    if not data.get("results"):
                        raise HTTPException(
                            status_code=404,
                            detail="No elevation data found for the given coordinates"
                        )
                    
                    return data["results"][0]["elevation"]
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error during elevation lookup: {e}")
            raise HTTPException(
                status_code=500,
                detail="Network error during elevation lookup"
            )
        except Exception as e:
            logger.error(f"Unexpected error during elevation lookup: {e}")
            raise HTTPException(
                status_code=500,
                detail="Unexpected error during elevation lookup"
            )
