"""
FastAPI routes for RoofSnap API
"""
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import logging
from typing import List

from app.db.database import get_db
from app.db.models import Analysis
from app.db.schemas import (
    GeocodeRequest, GeocodeResponse,
    AnalyzeRequest, AnalyzeResponse,
    AnalysisResponse, ErrorResponse
)
from app.services.geocoding import GeocodingService
from app.services.ai_analysis import AIAnalysisService
from app.services.report_generator import ReportGeneratorService

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
geocoding_service = GeocodingService()
ai_analysis_service = AIAnalysisService()
report_service = ReportGeneratorService()


@router.post("/geocode", response_model=GeocodeResponse)
async def geocode_address(request: GeocodeRequest):
    """
    Geocode an address to get latitude and longitude
    """
    try:
        result = await geocoding_service.geocode_address(request.address)
        return GeocodeResponse(**result)
    except Exception as e:
        logger.error(f"Geocoding error: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/analyze", response_model=AnalyzeResponse)
async def start_analysis(
    request: AnalyzeRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """
    Start a new roof analysis
    """
    try:
        # Create new analysis record
        analysis = Analysis(
            address=request.address,
            latitude=0.0,  # Will be updated during processing
            longitude=0.0,  # Will be updated during processing
            status="pending"
        )
        
        db.add(analysis)
        await db.commit()
        await db.refresh(analysis)
        
        # Start background processing
        background_tasks.add_task(
            process_analysis,
            analysis.id,
            request.address
        )
        
        return AnalyzeResponse(
            analysisId=analysis.id,
            message="Analysis started successfully"
        )
        
    except Exception as e:
        logger.error(f"Error starting analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to start analysis")


@router.get("/analyses/{analysis_id}", response_model=AnalysisResponse)
async def get_analysis(analysis_id: int, db: AsyncSession = Depends(get_db)):
    """
    Get analysis results by ID
    """
    try:
        result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
        analysis = result.scalar_one_or_none()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        return AnalysisResponse(**analysis.to_dict())
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving analysis: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve analysis")


@router.get("/analyses", response_model=List[AnalysisResponse])
async def list_analyses(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """
    List all analyses with pagination
    """
    try:
        result = await db.execute(
            select(Analysis)
            .order_by(Analysis.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        analyses = result.scalars().all()
        
        return [AnalysisResponse(**analysis.to_dict()) for analysis in analyses]
        
    except Exception as e:
        logger.error(f"Error listing analyses: {e}")
        raise HTTPException(status_code=500, detail="Failed to list analyses")


@router.get("/analyses/{analysis_id}/report/pdf")
async def download_pdf_report(analysis_id: int, db: AsyncSession = Depends(get_db)):
    """
    Download PDF report for analysis
    """
    try:
        result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
        analysis = result.scalar_one_or_none()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        if analysis.status != "completed":
            raise HTTPException(status_code=400, detail="Analysis not completed yet")
        
        # Generate PDF if not exists
        if not analysis.pdf_report_path:
            pdf_path = await report_service.generate_pdf_report(analysis.to_dict())
            
            # Update analysis with PDF path
            analysis.pdf_report_path = pdf_path
            await db.commit()
        
        return FileResponse(
            analysis.pdf_report_path,
            media_type="application/pdf",
            filename=f"solar-analysis-{analysis_id}.pdf"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating PDF report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate PDF report")


@router.get("/analyses/{analysis_id}/report/csv")
async def download_csv_report(analysis_id: int, db: AsyncSession = Depends(get_db)):
    """
    Download CSV report for analysis
    """
    try:
        result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
        analysis = result.scalar_one_or_none()
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        if analysis.status != "completed":
            raise HTTPException(status_code=400, detail="Analysis not completed yet")
        
        # Generate CSV if not exists
        if not analysis.csv_report_path:
            csv_path = await report_service.generate_csv_report(analysis.to_dict())
            
            # Update analysis with CSV path
            analysis.csv_report_path = csv_path
            await db.commit()
        
        return FileResponse(
            analysis.csv_report_path,
            media_type="text/csv",
            filename=f"solar-data-{analysis_id}.csv"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating CSV report: {e}")
        raise HTTPException(status_code=500, detail="Failed to generate CSV report")


async def process_analysis(analysis_id: int, address: str):
    """
    Background task to process analysis
    """
    from app.db.database import AsyncSessionLocal
    
    async with AsyncSessionLocal() as db:
        try:
            # Get analysis record
            result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
            analysis = result.scalar_one_or_none()
            
            if not analysis:
                logger.error(f"Analysis {analysis_id} not found")
                return
            
            # Update status to processing
            analysis.status = "processing"
            await db.commit()
            
            # Perform complete analysis
            logger.info(f"Starting complete analysis for {address}")
            complete_result = await ai_analysis_service.perform_complete_analysis(address)
            
            # Update analysis with results
            analysis.address = complete_result["address"]
            analysis.latitude = complete_result["latitude"]
            analysis.longitude = complete_result["longitude"]
            analysis.satellite_image_url = complete_result["satellite_image_url"]
            analysis.street_view_urls = complete_result["street_view_urls"]
            analysis.roof_area = complete_result["roof_area"]
            analysis.usable_area = complete_result["usable_area"]
            analysis.panel_count = complete_result["panel_count"]
            analysis.annual_kwh = complete_result["annual_kwh"]
            analysis.efficiency = complete_result["efficiency"]
            analysis.grade = complete_result["grade"]
            analysis.solar_grid = complete_result["solar_grid"]
            analysis.ai_summary = complete_result["ai_summary"]
            analysis.recommendations = complete_result["recommendations"]
            analysis.roof_tilt = complete_result.get("roof_tilt")
            analysis.orientation = complete_result.get("orientation")
            analysis.shading_level = complete_result.get("shading_level")
            analysis.status = "completed"
            
            await db.commit()
            
            logger.info(f"Analysis {analysis_id} completed successfully")
            
        except Exception as e:
            logger.error(f"Analysis {analysis_id} failed: {e}")
            
            # Update status to failed
            try:
                result = await db.execute(select(Analysis).where(Analysis.id == analysis_id))
                analysis = result.scalar_one_or_none()
                if analysis:
                    analysis.status = "failed"
                    await db.commit()
            except Exception as update_error:
                logger.error(f"Failed to update analysis status: {update_error}")
