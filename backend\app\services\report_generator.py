"""
Report generation service for PDF and CSV reports
"""
import os
import csv
import logging
from typing import Dict, Any, List
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ReportGeneratorService:
    """Service for generating PDF and CSV reports"""
    
    def __init__(self):
        self.data_dir = settings.data_directory
        os.makedirs(self.data_dir, exist_ok=True)
    
    async def generate_pdf_report(self, analysis_data: Dict[str, Any]) -> str:
        """
        Generate PDF report for analysis
        
        Args:
            analysis_data: Complete analysis data
            
        Returns:
            Path to generated PDF file
        """
        try:
            # Generate filename
            timestamp = int(datetime.now().timestamp() * 1000)
            filename = f"solar_report_{timestamp}.pdf"
            filepath = os.path.join(self.data_dir, filename)
            
            # Create PDF document
            doc = SimpleDocTemplate(filepath, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                textColor=colors.darkblue
            )
            story.append(Paragraph("Solar Analysis Report", title_style))
            story.append(Spacer(1, 12))
            
            # Property Information
            story.append(Paragraph("Property Information", styles['Heading2']))
            property_data = [
                ["Address:", analysis_data.get("address", "N/A")],
                ["Coordinates:", f"{analysis_data.get('latitude', 0):.6f}, {analysis_data.get('longitude', 0):.6f}"],
                ["Analysis Date:", datetime.now().strftime("%B %d, %Y")],
                ["Report Status:", analysis_data.get("status", "N/A").title()]
            ]
            
            property_table = Table(property_data, colWidths=[2*inch, 4*inch])
            property_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(property_table)
            story.append(Spacer(1, 20))
            
            # Solar Analysis Results
            story.append(Paragraph("Solar Analysis Results", styles['Heading2']))
            
            solar_data = [
                ["Total Solar Panels:", str(analysis_data.get("panel_count", 0))],
                ["Annual Energy Production:", f"{analysis_data.get('annual_kwh', 0):,.0f} kWh"],
                ["System Efficiency:", f"{analysis_data.get('efficiency', 0):.1%}"],
                ["Solar Grade:", analysis_data.get("grade", "N/A")],
                ["Roof Area:", f"{analysis_data.get('roof_area', 0):.1f} sq ft"],
                ["Usable Area:", f"{analysis_data.get('usable_area', 0):.1f} sq ft"]
            ]
            
            solar_table = Table(solar_data, colWidths=[2*inch, 4*inch])
            solar_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(solar_table)
            story.append(Spacer(1, 20))
            
            # Financial Estimates
            story.append(Paragraph("Financial Estimates", styles['Heading2']))
            annual_kwh = analysis_data.get("annual_kwh", 0)
            annual_savings = annual_kwh * 0.12  # $0.12 per kWh
            
            financial_data = [
                ["Estimated Annual Savings:", f"${annual_savings:,.0f}"],
                ["25-Year Savings:", f"${annual_savings * 25:,.0f}"],
                ["Electricity Rate (assumed):", "$0.12 per kWh"],
                ["Payback Period (estimated):", "8-12 years"]
            ]
            
            financial_table = Table(financial_data, colWidths=[2*inch, 4*inch])
            financial_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgreen),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(financial_table)
            story.append(Spacer(1, 20))
            
            # AI Summary
            ai_summary = analysis_data.get("ai_summary", "")
            if ai_summary:
                story.append(Paragraph("AI Analysis Summary", styles['Heading2']))
                story.append(Paragraph(ai_summary, styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Recommendations
            recommendations = analysis_data.get("recommendations", [])
            if recommendations:
                story.append(Paragraph("Recommendations", styles['Heading2']))
                for i, rec in enumerate(recommendations, 1):
                    story.append(Paragraph(f"{i}. {rec}", styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Footer
            story.append(Spacer(1, 30))
            footer_text = "This report is generated by RoofSnap AI analysis. Consult with certified solar installers for detailed system design and installation."
            story.append(Paragraph(footer_text, styles['Italic']))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"PDF report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating PDF report: {e}")
            raise
    
    async def generate_csv_report(self, analysis_data: Dict[str, Any]) -> str:
        """
        Generate CSV report for analysis
        
        Args:
            analysis_data: Complete analysis data
            
        Returns:
            Path to generated CSV file
        """
        try:
            # Generate filename
            timestamp = int(datetime.now().timestamp() * 1000)
            filename = f"solar_data_{timestamp}.csv"
            filepath = os.path.join(self.data_dir, filename)
            
            # Prepare data for CSV
            solar_grid = analysis_data.get("solar_grid", {})
            zones = solar_grid.get("zones", [])
            
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                
                # Write header information
                writer.writerow(["Solar Analysis Report"])
                writer.writerow(["Address", analysis_data.get("address", "N/A")])
                writer.writerow(["Analysis Date", datetime.now().strftime("%Y-%m-%d %H:%M:%S")])
                writer.writerow(["Latitude", analysis_data.get("latitude", 0)])
                writer.writerow(["Longitude", analysis_data.get("longitude", 0)])
                writer.writerow([])
                
                # Write summary data
                writer.writerow(["Summary"])
                writer.writerow(["Total Panels", analysis_data.get("panel_count", 0)])
                writer.writerow(["Annual kWh", analysis_data.get("annual_kwh", 0)])
                writer.writerow(["System Efficiency", analysis_data.get("efficiency", 0)])
                writer.writerow(["Solar Grade", analysis_data.get("grade", "N/A")])
                writer.writerow(["Roof Area (sq ft)", analysis_data.get("roof_area", 0)])
                writer.writerow(["Usable Area (sq ft)", analysis_data.get("usable_area", 0)])
                writer.writerow([])
                
                # Write panel details
                if zones:
                    writer.writerow(["Panel Details"])
                    writer.writerow(["Panel #", "X Position", "Y Position", "Width", "Height", "Potential", "Efficiency", "Estimated Output (W)"])
                    
                    for i, zone in enumerate(zones, 1):
                        writer.writerow([
                            i,
                            zone.get("x", 0),
                            zone.get("y", 0),
                            zone.get("width", 0),
                            zone.get("height", 0),
                            zone.get("potential", "unknown"),
                            zone.get("efficiency", 0),
                            zone.get("estimatedOutput", 0)
                        ])
                
                writer.writerow([])
                
                # Write recommendations
                recommendations = analysis_data.get("recommendations", [])
                if recommendations:
                    writer.writerow(["Recommendations"])
                    for i, rec in enumerate(recommendations, 1):
                        writer.writerow([f"{i}.", rec])
            
            logger.info(f"CSV report generated: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error generating CSV report: {e}")
            raise
