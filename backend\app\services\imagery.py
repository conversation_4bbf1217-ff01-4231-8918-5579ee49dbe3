"""
Imagery service for fetching satellite and street view images from Google APIs
"""
import aiohttp
import aiofiles
import os
import base64
import logging
from typing import Dict, List, Any
from fastapi import HTTPException

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class ImageryService:
    """Service for fetching satellite and street view images"""
    
    def __init__(self):
        self.api_key = settings.google_api_key
        self.data_dir = settings.data_directory
        
        if not self.api_key:
            logger.warning("Google API key not configured - imagery services will fail")
        
        # Ensure data directory exists
        os.makedirs(self.data_dir, exist_ok=True)
    
    async def fetch_satellite_image(
        self, 
        latitude: float, 
        longitude: float, 
        zoom: int = 19,
        size: str = "800x600"
    ) -> Dict[str, str]:
        """
        Fetch satellite image from Google Static Maps API
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            zoom: Zoom level (default 19 for high detail)
            size: Image size in format "widthxheight"
            
        Returns:
            Dict containing local file path and base64 data URL
        """
        if not self.api_key:
            raise HTTPException(
                status_code=500,
                detail="Google API key not configured"
            )
        
        url = "https://maps.googleapis.com/maps/api/staticmap"
        params = {
            "center": f"{latitude},{longitude}",
            "zoom": zoom,
            "size": size,
            "maptype": "satellite",
            "key": self.api_key
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status != 200:
                        raise HTTPException(
                            status_code=response.status,
                            detail=f"Failed to fetch satellite image: {response.status}"
                        )
                    
                    image_data = await response.read()
                    
                    # Generate filename with timestamp
                    import time
                    timestamp = int(time.time() * 1000)
                    filename = f"satellite_{latitude}_{longitude}_{timestamp}.png"
                    filepath = os.path.join(self.data_dir, filename)
                    
                    # Save image to file
                    async with aiofiles.open(filepath, 'wb') as f:
                        await f.write(image_data)
                    
                    # Create base64 data URL
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    data_url = f"data:image/png;base64,{base64_data}"
                    
                    return {
                        "local_path": filepath,
                        "data_url": data_url,
                        "filename": filename
                    }
                    
        except aiohttp.ClientError as e:
            logger.error(f"Network error fetching satellite image: {e}")
            raise HTTPException(
                status_code=500,
                detail="Network error fetching satellite image"
            )
        except Exception as e:
            logger.error(f"Unexpected error fetching satellite image: {e}")
            raise HTTPException(
                status_code=500,
                detail="Unexpected error fetching satellite image"
            )
    
    async def fetch_street_view_images(
        self,
        latitude: float,
        longitude: float,
        size: str = "600x400",
        headings: List[int] = None
    ) -> List[Dict[str, str]]:
        """
        Fetch street view images from multiple angles
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            size: Image size in format "widthxheight"
            headings: List of compass headings (default: [0, 90, 180, 270])
            
        Returns:
            List of dicts containing local file paths and base64 data URLs
        """
        if not self.api_key:
            raise HTTPException(
                status_code=500,
                detail="Google API key not configured"
            )
        
        if headings is None:
            headings = [0, 90, 180, 270]  # North, East, South, West
        
        results = []
        
        for heading in headings:
            url = "https://maps.googleapis.com/maps/api/streetview"
            params = {
                "size": size,
                "location": f"{latitude},{longitude}",
                "heading": heading,
                "pitch": 10,
                "key": self.api_key
            }
            
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status != 200:
                            logger.warning(f"Failed to fetch street view for heading {heading}: {response.status}")
                            continue
                        
                        image_data = await response.read()
                        
                        # Generate filename with timestamp
                        import time
                        timestamp = int(time.time() * 1000)
                        filename = f"streetview_{latitude}_{longitude}_{heading}_{timestamp}.png"
                        filepath = os.path.join(self.data_dir, filename)
                        
                        # Save image to file
                        async with aiofiles.open(filepath, 'wb') as f:
                            await f.write(image_data)
                        
                        # Create base64 data URL
                        base64_data = base64.b64encode(image_data).decode('utf-8')
                        data_url = f"data:image/png;base64,{base64_data}"
                        
                        results.append({
                            "local_path": filepath,
                            "data_url": data_url,
                            "filename": filename,
                            "heading": heading
                        })
                        
            except aiohttp.ClientError as e:
                logger.warning(f"Network error fetching street view for heading {heading}: {e}")
                continue
            except Exception as e:
                logger.warning(f"Unexpected error fetching street view for heading {heading}: {e}")
                continue
        
        return results
    
    async def fetch_all_images(
        self,
        latitude: float,
        longitude: float
    ) -> Dict[str, Any]:
        """
        Fetch both satellite and street view images
        
        Args:
            latitude: Latitude coordinate
            longitude: Longitude coordinate
            
        Returns:
            Dict containing satellite and street view image data
        """
        # Fetch satellite image
        satellite_result = await self.fetch_satellite_image(latitude, longitude)
        
        # Fetch street view images
        street_view_results = await self.fetch_street_view_images(latitude, longitude)
        
        return {
            "satellite": satellite_result,
            "street_views": street_view_results,
            "satellite_image_url": satellite_result["data_url"],
            "street_view_urls": [sv["data_url"] for sv in street_view_results],
            "local_paths": {
                "satellite": satellite_result["local_path"],
                "street_views": [sv["local_path"] for sv in street_view_results]
            }
        }
