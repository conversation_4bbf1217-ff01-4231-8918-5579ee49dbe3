"""
SQLAlchemy models for RoofSnap database
"""
from sqlalchemy import Column, Integer, String, Float, Text, DateTime, JSON, Boolean
from sqlalchemy.sql import func
from datetime import datetime
from typing import List, Dict, Any, Optional

from app.db.database import Base


class Analysis(Base):
    """
    Analysis model matching the existing schema
    """
    __tablename__ = "analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    address = Column(String, nullable=False)
    latitude = Column(Float, nullable=False)
    longitude = Column(Float, nullable=False)
    status = Column(String, nullable=False, default="pending")  # pending, processing, completed, failed
    
    # Image URLs
    satellite_image_url = Column(Text, nullable=True)
    street_view_urls = Column(JSON, nullable=True)  # List[str]
    
    # AI Analysis Results
    roof_area = Column(Float, nullable=True)  # in sq ft
    usable_area = Column(Float, nullable=True)  # in sq ft
    panel_count = Column(Integer, nullable=True)
    annual_kwh = Column(Float, nullable=True)
    efficiency = Column(Float, nullable=True)  # 0-1
    grade = Column(String, nullable=True)  # A+, A, A-, B+, etc.
    
    # Property Details
    roof_tilt = Column(Float, nullable=True)  # degrees
    orientation = Column(String, nullable=True)  # South-facing, etc.
    shading_level = Column(String, nullable=True)  # Minimal, Moderate, High
    
    # Solar Grid Data
    solar_grid = Column(JSON, nullable=True)  # Complex nested structure
    
    # Generated Reports
    pdf_report_path = Column(Text, nullable=True)
    csv_report_path = Column(Text, nullable=True)
    
    # NLP Summary
    ai_summary = Column(Text, nullable=True)
    recommendations = Column(JSON, nullable=True)  # List[str]
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "address": self.address,
            "latitude": self.latitude,
            "longitude": self.longitude,
            "status": self.status,
            "satelliteImageUrl": self.satellite_image_url,
            "streetViewUrls": self.street_view_urls,
            "roofArea": self.roof_area,
            "usableArea": self.usable_area,
            "panelCount": self.panel_count,
            "annualKwh": self.annual_kwh,
            "efficiency": self.efficiency,
            "grade": self.grade,
            "roofTilt": self.roof_tilt,
            "orientation": self.orientation,
            "shadingLevel": self.shading_level,
            "solarGrid": self.solar_grid,
            "pdfReportPath": self.pdf_report_path,
            "csvReportPath": self.csv_report_path,
            "aiSummary": self.ai_summary,
            "recommendations": self.recommendations,
            "createdAt": self.created_at.isoformat() if self.created_at else None,
            "updatedAt": self.updated_at.isoformat() if self.updated_at else None,
        }


class User(Base):
    """
    User model for authentication (keeping for compatibility)
    """
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, nullable=False, index=True)
    password = Column(String, nullable=False)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary"""
        return {
            "id": self.id,
            "username": self.username,
            # Note: password is intentionally excluded from dict representation
        }
