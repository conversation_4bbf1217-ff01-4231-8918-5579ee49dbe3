import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Download, FileText, Share, Printer, Home, Sun, Battery, TrendingUp, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import type { Analysis } from "@shared/schema";

interface AnalysisSummaryProps {
  analysis: Analysis;
}

export default function AnalysisSummary({ analysis }: AnalysisSummaryProps) {
  const { toast } = useToast();

  const downloadPdfMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("GET", `/api/analyses/${analysis.id}/report/pdf`);
      return res.blob();
    },
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `solar-analysis-${analysis.id}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast({
        title: "PDF Downloaded",
        description: "Professional solar analysis report has been saved to your device.",
      });
    },
    onError: () => {
      toast({
        title: "Download Failed",
        description: "Unable to generate PDF report. Please try again.",
        variant: "destructive",
      });
    },
  });

  const downloadCsvMutation = useMutation({
    mutationFn: async () => {
      const res = await apiRequest("GET", `/api/analyses/${analysis.id}/report/csv`);
      return res.blob();
    },
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `solar-data-${analysis.id}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      toast({
        title: "CSV Downloaded",
        description: "Solar analysis data has been exported to CSV format.",
      });
    },
    onError: () => {
      toast({
        title: "Export Failed",
        description: "Unable to export CSV data. Please try again.",
        variant: "destructive",
      });
    },
  });

  const getGradeColor = (grade: string) => {
    if (grade?.startsWith('A')) return 'bg-green-600 text-white';
    if (grade?.startsWith('B')) return 'bg-yellow-600 text-white';
    if (grade?.startsWith('C')) return 'bg-orange-500 text-white';
    return 'bg-red-600 text-white';
  };

  const keyMetrics = [
    {
      icon: Home,
      label: "Total Roof Area",
      value: `${analysis.roofArea?.toLocaleString() || 0} sq ft`,
      description: "Complete roof surface area analyzed",
      color: "text-blue-600",
      bg: "bg-blue-50"
    },
    {
      icon: Sun,
      label: "Usable Solar Area",
      value: `${analysis.usableArea?.toLocaleString() || 0} sq ft`,
      description: "Area suitable for panel installation",
      color: "text-green-600",
      bg: "bg-green-50"
    },
    {
      icon: Battery,
      label: "Recommended Panels",
      value: `${analysis.panelCount || 0} panels`,
      description: "Optimal panel configuration",
      color: "text-orange-600",
      bg: "bg-orange-50"
    },
    {
      icon: TrendingUp,
      label: "Annual Energy Output",
      value: `${analysis.annualKwh?.toLocaleString() || 0} kWh`,
      description: "Estimated yearly energy production",
      color: "text-purple-600",
      bg: "bg-purple-50"
    }
  ];

  const usablePercentage = analysis.roofArea ? ((analysis.usableArea || 0) / analysis.roofArea * 100) : 0;
  const efficiencyPercentage = (analysis.efficiency || 0) * 100;

  const highZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'high').length || 0;
  const mediumZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'medium').length || 0;
  const lowZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'low').length || 0;
  const unusableZones = analysis.solarGrid?.zones?.filter(z => z.potential === 'unusable').length || 0;

  return (
    <div className="space-y-6 max-w-6xl mx-auto">
      {/* Header with Overall Grade */}
      <div className="text-center space-y-4">
        <h2 className="text-3xl font-bold text-gray-800">Professional Solar Analysis Report</h2>
        <p className="text-lg text-gray-600">{analysis.address}</p>
        <div className="flex justify-center items-center gap-4">
          <Badge className={`text-2xl px-6 py-2 ${getGradeColor(analysis.grade || '')}`}>
            Overall Grade: {analysis.grade || 'Calculating...'}
          </Badge>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {efficiencyPercentage.toFixed(1)}%
            </div>
            <div className="text-sm text-gray-600">System Efficiency</div>
          </div>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {keyMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className={`w-12 h-12 ${metric.bg} rounded-xl flex items-center justify-center`}>
                    <Icon className={`h-6 w-6 ${metric.color}`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-600">{metric.label}</p>
                    <p className="text-xl font-bold text-gray-800">{metric.value}</p>
                    <p className="text-xs text-gray-500">{metric.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Solar Zone Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Zone Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sun className="h-6 w-6 text-yellow-500" />
              Solar Zone Distribution
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{highZones}</div>
                <div className="text-sm text-green-700">High Energy Zones</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{mediumZones}</div>
                <div className="text-sm text-yellow-700">Medium Energy Zones</div>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{lowZones}</div>
                <div className="text-sm text-orange-700">Low Energy Zones</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{unusableZones}</div>
                <div className="text-sm text-red-700">Unusable Zones</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-6 w-6 text-blue-500" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Roof Utilization</span>
                <span>{usablePercentage.toFixed(1)}%</span>
              </div>
              <Progress value={usablePercentage} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>System Efficiency</span>
                <span>{efficiencyPercentage.toFixed(1)}%</span>
              </div>
              <Progress value={efficiencyPercentage} className="h-2" />
            </div>
            <div className="pt-2 space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Roof Tilt:</span>
                <span className="font-medium">{analysis.roofTilt || 'N/A'}°</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Orientation:</span>
                <span className="font-medium">{analysis.orientation || 'N/A'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Shading Level:</span>
                <span className="font-medium">{analysis.shadingLevel || 'N/A'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* AI Summary */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-800">
            <CheckCircle className="h-6 w-6" />
            AI-Generated Analysis Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-white p-6 rounded-lg mb-4">
            <p className="text-gray-800 leading-relaxed text-lg">
              {analysis.aiSummary || 'AI summary is being generated...'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
            Professional Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {analysis.recommendations?.map((recommendation, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-sm font-semibold text-white">{index + 1}</span>
                </div>
                <p className="text-gray-700 leading-relaxed">{recommendation}</p>
              </div>
            )) || (
              <p className="text-gray-500 italic">Recommendations are being generated...</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Download Section */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-green-800">
            <Download className="h-6 w-6" />
            Export Professional Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              onClick={() => downloadPdfMutation.mutate()}
              disabled={downloadPdfMutation.isPending}
              className="bg-red-600 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all"
            >
              {downloadPdfMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Generating PDF...
                </>
              ) : (
                <>
                  <FileText className="h-5 w-5 mr-2" />
                  Download PDF Report
                </>
              )}
            </Button>
            
            <Button
              onClick={() => downloadCsvMutation.mutate()}
              disabled={downloadCsvMutation.isPending}
              variant="outline"
              className="border-green-300 text-green-700 hover:bg-green-50 font-semibold py-3 px-6 rounded-lg shadow-lg hover:shadow-xl transition-all"
            >
              {downloadCsvMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-green-600 mr-2"></div>
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="h-5 w-5 mr-2" />
                  Export CSV Data
                </>
              )}
            </Button>
          </div>
          
          <div className="mt-4 p-4 bg-white rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">Report Contents:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Comprehensive solar potential analysis</li>
              <li>• Detailed roof assessment and zone mapping</li>
              <li>• Energy production estimates and financial projections</li>
              <li>• Professional installation recommendations</li>
              <li>• Technical specifications and system requirements</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}