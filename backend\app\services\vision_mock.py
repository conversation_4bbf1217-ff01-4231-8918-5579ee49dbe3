"""
Mock computer vision services for localhost testing (without OpenCV dependencies)
"""
import logging
import random
import base64
import io
from typing import List, Dict, Any, Tuple
from PIL import Image, ImageDraw

from app.core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class MockRoofDetector:
    """Mock roof detection for testing without OpenCV"""
    
    def __init__(self):
        self.min_roof_area = 50
        
    def detect_roof(self, image_data: bytes) -> Dict[str, Any]:
        """Mock roof detection that returns a reasonable roof polygon"""
        # Create a mock rectangular roof in the center
        roof_polygon = [
            [200, 150],  # Top-left
            [440, 150],  # Top-right
            [440, 350],  # Bottom-right
            [200, 350]   # Bottom-left
        ]
        
        return {
            "roof_polygon": roof_polygon,
            "confidence": 0.85,
            "area_pixels": 57600,  # 240 * 240
            "detected_contours": 1
        }


class MockSolarPanelPlacer:
    """Mock solar panel placement for testing"""
    
    def __init__(self):
        self.panel_spacing = 0.3
        self.panel_width = settings.panel_width
        self.panel_height = settings.panel_height
        self.panel_area = self.panel_width * self.panel_height
        
    def pixel_to_meters(self, pixel_distance: float, zoom_level: int = 19) -> float:
        """Mock conversion - simplified"""
        return pixel_distance * 0.3  # Rough approximation
    
    def meters_to_pixels(self, meters: float, zoom_level: int = 19) -> float:
        """Mock conversion - simplified"""
        return meters / 0.3
    
    def generate_solar_grid(
        self, 
        roof_polygon: List[List[float]], 
        image_shape: tuple = (640, 640), 
        zoom_level: int = 19
    ) -> Dict[str, Any]:
        """Generate mock solar panel grid"""
        
        # Calculate bounding box
        xs = [p[0] for p in roof_polygon]
        ys = [p[1] for p in roof_polygon]
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # Mock panel dimensions in pixels
        panel_width_px = 50
        panel_height_px = 30
        spacing_px = 10
        
        panels = []
        panel_id = 1
        
        # Generate grid of panels
        y = min_y + 20
        while y + panel_height_px <= max_y - 20:
            x = min_x + 20
            while x + panel_width_px <= max_x - 20:
                # Vary efficiency based on position
                center_x = (min_x + max_x) / 2
                center_y = (min_y + max_y) / 2
                distance_from_center = ((x - center_x)**2 + (y - center_y)**2)**0.5
                max_distance = ((max_x - min_x)**2 + (max_y - min_y)**2)**0.5 / 2
                
                efficiency = max(0.4, 1.0 - (distance_from_center / max_distance))
                
                # Determine potential level
                if efficiency > 0.8:
                    potential = "high"
                elif efficiency > 0.6:
                    potential = "medium"
                elif efficiency > 0.4:
                    potential = "low"
                else:
                    potential = "unusable"
                
                panels.append({
                    "x": float(x),
                    "y": float(y),
                    "width": float(panel_width_px),
                    "height": float(panel_height_px),
                    "potential": potential,
                    "efficiency": float(efficiency),
                    "estimated_output": float(efficiency * 300)  # 300W base
                })
                
                x += panel_width_px + spacing_px
                panel_id += 1
            y += panel_height_px + spacing_px
        
        roof_area_m2 = self.pixel_to_meters((max_x - min_x) * (max_y - min_y), zoom_level)
        
        return {
            "panels": panels,
            "total_panels": len(panels),
            "total_output": sum(p["estimated_output"] for p in panels),
            "roof_area_m2": roof_area_m2,
            "panel_coverage": len(panels) * self.panel_area
        }


class MockSolarCalculator:
    """Mock solar calculations for testing"""
    
    def calculate_solar_potential(
        self, 
        panels: List[Dict], 
        latitude: float, 
        longitude: float
    ) -> Dict[str, Any]:
        """Mock solar potential calculation"""
        
        if not panels:
            return {
                "total_panels": 0,
                "annual_kwh": 0,
                "total_output": 0,
                "efficiency": 0,
                "grade": "F",
                "panel_breakdown": {"high": 0, "medium": 0, "low": 0}
            }
        
        total_panels = len(panels)
        total_output = sum(p["estimated_output"] for p in panels)
        
        # Mock annual kWh calculation
        peak_sun_hours = 4.5
        annual_kwh = (total_output / 1000) * peak_sun_hours * 365
        
        # Calculate breakdown
        high_potential = len([p for p in panels if p["potential"] == "high"])
        medium_potential = len([p for p in panels if p["potential"] == "medium"])
        low_potential = len([p for p in panels if p["potential"] == "low"])
        
        # Determine grade
        if high_potential / total_panels > 0.7:
            grade = "A"
        elif high_potential / total_panels > 0.5:
            grade = "B"
        elif medium_potential / total_panels > 0.5:
            grade = "C"
        else:
            grade = "D"
        
        return {
            "total_panels": total_panels,
            "annual_kwh": round(annual_kwh, 0),
            "total_output": round(total_output, 0),
            "efficiency": round(sum(p["efficiency"] for p in panels) / total_panels, 2),
            "grade": grade,
            "panel_breakdown": {
                "high": high_potential,
                "medium": medium_potential,
                "low": low_potential
            }
        }


class MockVisionService:
    """Mock vision service for localhost testing"""
    
    def __init__(self):
        self.roof_detector = MockRoofDetector()
        self.panel_placer = MockSolarPanelPlacer()
        self.solar_calculator = MockSolarCalculator()
    
    async def analyze_roof_from_image(
        self,
        image_data: bytes,
        latitude: float,
        longitude: float,
        zoom: int = 19
    ) -> Dict[str, Any]:
        """Mock roof analysis"""
        try:
            # Mock roof detection
            roof_result = self.roof_detector.detect_roof(image_data)
            
            # Mock solar panel layout
            solar_grid = self.panel_placer.generate_solar_grid(
                roof_result["roof_polygon"], 
                (640, 640),
                zoom
            )
            
            # Mock solar potential calculation
            solar_analysis = self.solar_calculator.calculate_solar_potential(
                solar_grid["panels"],
                latitude,
                longitude
            )
            
            # Generate mock visualization
            visualization = self.generate_mock_visualization(
                roof_result["roof_polygon"],
                solar_grid["panels"]
            )
            
            return {
                "roof_detection": roof_result,
                "solar_grid": solar_grid,
                "solar_analysis": solar_analysis,
                "visualization": visualization,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Mock analysis error: {e}")
            return {
                "error": str(e),
                "success": False
            }
    
    def generate_mock_visualization(
        self,
        roof_polygon: List[List[float]],
        panels: List[Dict[str, Any]]
    ) -> str:
        """Generate a simple mock visualization"""
        try:
            # Create a simple image with PIL
            img = Image.new('RGB', (640, 480), color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # Draw roof outline
            if roof_polygon:
                draw.polygon([tuple(p) for p in roof_polygon], outline='yellow', width=3)
            
            # Draw panels as colored rectangles
            color_map = {
                'high': 'green',
                'medium': 'orange', 
                'low': 'red',
                'unusable': 'gray'
            }
            
            for panel in panels:
                color = color_map.get(panel['potential'], 'gray')
                x, y = panel['x'], panel['y']
                w, h = panel['width'], panel['height']
                draw.rectangle([x, y, x+w, y+h], fill=color, outline='white')
            
            # Convert to base64
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            
            image_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
            return f"data:image/png;base64,{image_base64}"
            
        except Exception as e:
            logger.error(f"Error generating mock visualization: {e}")
            return ""
