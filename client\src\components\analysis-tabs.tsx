import { useState } from "react";
import { Map, View, TrendingUp, Download } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import MapView from "./map-view";
import StreetViews from "./street-views";
import Summary from "./summary";
import DownloadSection from "./download";
import type { Analysis } from "@shared/schema";

interface AnalysisTabsProps {
  analysis: Analysis;
}

export default function AnalysisTabs({ analysis }: AnalysisTabsProps) {
  const [activeTab, setActiveTab] = useState<'map' | 'street' | 'summary' | 'download'>('map');

  const tabs = [
    { id: 'map', label: 'Map View', icon: Map },
    { id: 'street', label: 'Street Views', icon: View },
    { id: 'summary', label: 'Summary', icon: TrendingUp },
    { id: 'download', label: 'Download', icon: Download },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'map':
        return <MapView analysis={analysis} />;
      case 'street':
        return <StreetViews analysis={analysis} />;
      case 'summary':
        return <Summary analysis={analysis} />;
      case 'download':
        return <DownloadSection analysis={analysis} />;
      default:
        return <MapView analysis={analysis} />;
    }
  };

  return (
    <Card className="mb-8">
      <div className="border-b">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Button
                key={tab.id}
                variant="ghost"
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => setActiveTab(tab.id as any)}
              >
                <Icon className="h-4 w-4 mr-2" />
                {tab.label}
              </Button>
            );
          })}
        </nav>
      </div>
      
      <CardContent className="p-6">
        {renderTabContent()}
      </CardContent>
    </Card>
  );
}
